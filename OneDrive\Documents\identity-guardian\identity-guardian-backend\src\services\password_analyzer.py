import re
import string
import secrets
import hashlib
import requests
from typing import Dict, List, Tuple
from src.models.password import PasswordStrength, PasswordStatus

class PasswordAnalyzer:
    """Service for analyzing password strength and security."""
    
    def __init__(self):
        self.common_passwords = self._load_common_passwords()
        self.password_patterns = self._compile_patterns()
    
    def analyze_password(self, password: str) -> Dict:
        """
        Comprehensive password analysis including strength and security assessment.
        
        Args:
            password: The password to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            'strength': self._calculate_strength(password),
            'score': self._calculate_score(password),
            'issues': self._identify_issues(password),
            'suggestions': self._generate_suggestions(password),
            'entropy': self._calculate_entropy(password),
            'time_to_crack': self._estimate_crack_time(password)
        }
        
        return analysis
    
    def check_compromised_status(self, password: str) -> Tuple[PasswordStatus, Dict]:
        """
        Check if password appears in known breach databases using HIBP API.
        
        Args:
            password: The password to check
            
        Returns:
            Tuple of (status, details)
        """
        try:
            # Generate SHA-1 hash for HIBP API
            sha1_hash = hashlib.sha1(password.encode('utf-8')).hexdigest().upper()
            prefix = sha1_hash[:5]
            suffix = sha1_hash[5:]
            
            # Query HIBP API with k-anonymity
            response = requests.get(
                f"https://api.pwnedpasswords.com/range/{prefix}",
                timeout=10
            )
            
            if response.status_code == 200:
                # Check if our hash suffix appears in the results
                for line in response.text.splitlines():
                    hash_suffix, count = line.split(':')
                    if hash_suffix == suffix:
                        return PasswordStatus.COMPROMISED, {
                            'breach_count': int(count),
                            'source': 'HaveIBeenPwned',
                            'checked_at': 'now'
                        }
                
                return PasswordStatus.SAFE, {
                    'source': 'HaveIBeenPwned',
                    'checked_at': 'now'
                }
            
            else:
                return PasswordStatus.UNKNOWN, {
                    'error': f"API returned status {response.status_code}",
                    'source': 'HaveIBeenPwned'
                }
                
        except Exception as e:
            return PasswordStatus.UNKNOWN, {
                'error': str(e),
                'source': 'HaveIBeenPwned'
            }
    
    def generate_strong_password(self, length: int = 16, include_symbols: bool = True) -> str:
        """
        Generate a cryptographically secure strong password.
        
        Args:
            length: Desired password length (minimum 12)
            include_symbols: Whether to include special characters
            
        Returns:
            Generated strong password
        """
        if length < 12:
            length = 12
        
        # Define character sets
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?" if include_symbols else ""
        
        # Ensure at least one character from each required set
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits)
        ]
        
        if include_symbols:
            password.append(secrets.choice(symbols))
        
        # Fill remaining length with random characters from all sets
        all_chars = lowercase + uppercase + digits + symbols
        for _ in range(length - len(password)):
            password.append(secrets.choice(all_chars))
        
        # Shuffle the password to avoid predictable patterns
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    def generate_passphrase(self, word_count: int = 4, separator: str = '-') -> str:
        """
        Generate a memorable passphrase using random words.
        
        Args:
            word_count: Number of words in the passphrase
            separator: Character to separate words
            
        Returns:
            Generated passphrase
        """
        # Common word list for passphrases (in production, use a larger dictionary)
        words = [
            'apple', 'bridge', 'castle', 'dragon', 'eagle', 'forest', 'guitar', 'harbor',
            'island', 'jungle', 'knight', 'ladder', 'mountain', 'ocean', 'palace', 'queen',
            'river', 'sunset', 'tiger', 'umbrella', 'village', 'wizard', 'yellow', 'zebra',
            'anchor', 'balloon', 'candle', 'diamond', 'elephant', 'feather', 'garden', 'hammer',
            'iceberg', 'jacket', 'keyboard', 'lantern', 'mirror', 'notebook', 'orange', 'pencil'
        ]
        
        selected_words = [secrets.choice(words) for _ in range(word_count)]
        
        # Capitalize first letter of each word and add random numbers
        passphrase_words = []
        for word in selected_words:
            # Randomly capitalize and add numbers
            if secrets.randbelow(2):
                word = word.capitalize()
            if secrets.randbelow(3) == 0:  # 33% chance to add a number
                word += str(secrets.randbelow(100))
            passphrase_words.append(word)
        
        return separator.join(passphrase_words)
    
    def _calculate_strength(self, password: str) -> PasswordStrength:
        """Calculate password strength enum based on score."""
        score = self._calculate_score(password)
        
        if score >= 90:
            return PasswordStrength.VERY_STRONG
        elif score >= 75:
            return PasswordStrength.STRONG
        elif score >= 60:
            return PasswordStrength.GOOD
        elif score >= 40:
            return PasswordStrength.FAIR
        elif score >= 20:
            return PasswordStrength.WEAK
        else:
            return PasswordStrength.VERY_WEAK
    
    def _calculate_score(self, password: str) -> int:
        """Calculate password strength score (0-100)."""
        score = 0
        
        # Length scoring (0-25 points)
        length = len(password)
        if length >= 16:
            score += 25
        elif length >= 12:
            score += 20
        elif length >= 8:
            score += 15
        elif length >= 6:
            score += 10
        else:
            score += 5
        
        # Character variety scoring (0-40 points)
        has_lower = bool(re.search(r'[a-z]', password))
        has_upper = bool(re.search(r'[A-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_symbol = bool(re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password))
        
        char_variety = sum([has_lower, has_upper, has_digit, has_symbol])
        score += char_variety * 10
        
        # Pattern penalties (0 to -30 points)
        if self._has_common_patterns(password):
            score -= 10
        if self._is_common_password(password):
            score -= 20
        if self._has_repeated_chars(password):
            score -= 10
        
        # Entropy bonus (0-15 points)
        entropy = self._calculate_entropy(password)
        if entropy >= 60:
            score += 15
        elif entropy >= 40:
            score += 10
        elif entropy >= 25:
            score += 5
        
        # Uniqueness bonus (0-20 points)
        unique_chars = len(set(password))
        uniqueness_ratio = unique_chars / len(password) if len(password) > 0 else 0
        score += int(uniqueness_ratio * 20)
        
        return max(0, min(100, score))
    
    def _identify_issues(self, password: str) -> List[str]:
        """Identify specific issues with the password."""
        issues = []
        
        if len(password) < 8:
            issues.append("Password is too short (minimum 8 characters)")
        
        if not re.search(r'[a-z]', password):
            issues.append("Missing lowercase letters")
        
        if not re.search(r'[A-Z]', password):
            issues.append("Missing uppercase letters")
        
        if not re.search(r'\d', password):
            issues.append("Missing numbers")
        
        if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
            issues.append("Missing special characters")
        
        if self._is_common_password(password):
            issues.append("Password appears in common password lists")
        
        if self._has_common_patterns(password):
            issues.append("Contains common patterns (e.g., 123, abc, qwerty)")
        
        if self._has_repeated_chars(password):
            issues.append("Contains repeated character sequences")
        
        return issues
    
    def _generate_suggestions(self, password: str) -> List[str]:
        """Generate specific suggestions for improving the password."""
        suggestions = []
        
        if len(password) < 12:
            suggestions.append("Increase length to at least 12 characters")
        
        if not re.search(r'[a-z]', password):
            suggestions.append("Add lowercase letters")
        
        if not re.search(r'[A-Z]', password):
            suggestions.append("Add uppercase letters")
        
        if not re.search(r'\d', password):
            suggestions.append("Add numbers")
        
        if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
            suggestions.append("Add special characters (!@#$%^&*)")
        
        if self._is_common_password(password):
            suggestions.append("Avoid common passwords - use a unique combination")
        
        if self._has_common_patterns(password):
            suggestions.append("Avoid predictable patterns like 123 or abc")
        
        suggestions.append("Consider using a passphrase with random words")
        suggestions.append("Use a password manager to generate and store strong passwords")
        
        return suggestions
    
    def _calculate_entropy(self, password: str) -> float:
        """Calculate password entropy in bits."""
        charset_size = 0
        
        if re.search(r'[a-z]', password):
            charset_size += 26
        if re.search(r'[A-Z]', password):
            charset_size += 26
        if re.search(r'\d', password):
            charset_size += 10
        if re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
            charset_size += 32
        
        if charset_size == 0:
            return 0
        
        import math
        return len(password) * math.log2(charset_size)
    
    def _estimate_crack_time(self, password: str) -> str:
        """Estimate time to crack password using brute force."""
        entropy = self._calculate_entropy(password)
        
        # Assume 1 billion guesses per second
        guesses_per_second = 1e9
        total_combinations = 2 ** entropy
        seconds_to_crack = total_combinations / (2 * guesses_per_second)  # Average case
        
        if seconds_to_crack < 60:
            return "Less than 1 minute"
        elif seconds_to_crack < 3600:
            return f"{int(seconds_to_crack / 60)} minutes"
        elif seconds_to_crack < 86400:
            return f"{int(seconds_to_crack / 3600)} hours"
        elif seconds_to_crack < 31536000:
            return f"{int(seconds_to_crack / 86400)} days"
        elif seconds_to_crack < 31536000000:
            return f"{int(seconds_to_crack / 31536000)} years"
        else:
            return "Centuries"
    
    def _load_common_passwords(self) -> set:
        """Load common passwords list."""
        # In production, load from a comprehensive file
        return {
            'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
            'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',
            'qwerty123', 'dragon', 'master', 'hello', 'login', 'welcome123'
        }
    
    def _compile_patterns(self) -> Dict:
        """Compile common password patterns."""
        return {
            'sequential_numbers': re.compile(r'(012|123|234|345|456|567|678|789)'),
            'sequential_letters': re.compile(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', re.IGNORECASE),
            'keyboard_patterns': re.compile(r'(qwer|asdf|zxcv|1234|qaz|wsx)', re.IGNORECASE),
            'repeated_chars': re.compile(r'(.)\1{2,}'),
            'common_substitutions': re.compile(r'[@4aA]|[30oO]|[!1iI]|[$5sS]', re.IGNORECASE)
        }
    
    def _has_common_patterns(self, password: str) -> bool:
        """Check if password contains common patterns."""
        for pattern in self.password_patterns.values():
            if pattern.search(password):
                return True
        return False
    
    def _is_common_password(self, password: str) -> bool:
        """Check if password is in common passwords list."""
        return password.lower() in self.common_passwords
    
    def _has_repeated_chars(self, password: str) -> bool:
        """Check if password has repeated character sequences."""
        return bool(self.password_patterns['repeated_chars'].search(password))


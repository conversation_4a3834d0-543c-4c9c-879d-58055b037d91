-- =====================================================
-- Identity Guardian Database Schema
-- Database Name: theguardian
-- PostgreSQL Version: 13+
-- Created: 2025-01-08
-- Purpose: Complete database schema for Identity Guardian cybersecurity application
-- =====================================================

-- Create database (run this separately as superuser)
-- CREATE DATABASE theguardian;
-- \c theguardian;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "citext";

-- =====================================================
-- USERS AND AUTHENTICATION
-- =====================================================

-- Users table - Core user accounts
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email CITEXT UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    timezone VARCHAR(50) DEFAULT 'UTC',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User sessions for authentication tracking
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    location_data JSONB,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Two-factor authentication
CREATE TABLE user_2fa (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    method VARCHAR(20) NOT NULL CHECK (method IN ('totp', 'sms', 'email')),
    secret_key VARCHAR(255),
    backup_codes TEXT[],
    is_enabled BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, method)
);

-- =====================================================
-- IDENTITY MONITORING
-- =====================================================

-- Monitored identities (emails, phone numbers, etc.)
CREATE TABLE monitored_identities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    identity_type VARCHAR(20) NOT NULL CHECK (identity_type IN ('email', 'phone', 'username', 'ssn')),
    identity_value CITEXT NOT NULL,
    display_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    risk_level VARCHAR(20) DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    last_checked_at TIMESTAMP WITH TIME ZONE,
    breach_count INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, identity_type, identity_value)
);

-- Data breaches database
CREATE TABLE data_breaches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    company VARCHAR(255),
    description TEXT,
    breach_date DATE,
    discovered_date DATE,
    reported_date DATE,
    affected_accounts BIGINT,
    data_types TEXT[],
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    source_url TEXT,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Identity breach associations
CREATE TABLE identity_breaches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    monitored_identity_id UUID NOT NULL REFERENCES monitored_identities(id) ON DELETE CASCADE,
    data_breach_id UUID NOT NULL REFERENCES data_breaches(id) ON DELETE CASCADE,
    discovered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    notification_sent_at TIMESTAMP WITH TIME ZONE,
    user_acknowledged_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'false_positive')),
    additional_data JSONB,
    UNIQUE(monitored_identity_id, data_breach_id)
);

-- Dark web monitoring results
CREATE TABLE dark_web_findings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    monitored_identity_id UUID NOT NULL REFERENCES monitored_identities(id) ON DELETE CASCADE,
    source_type VARCHAR(50) NOT NULL,
    source_name VARCHAR(255),
    finding_type VARCHAR(50) NOT NULL,
    content_hash VARCHAR(64),
    risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
    confidence_level VARCHAR(20) CHECK (confidence_level IN ('low', 'medium', 'high')),
    raw_data JSONB,
    discovered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- =====================================================
-- PASSWORD MANAGEMENT
-- =====================================================

-- User passwords vault
CREATE TABLE user_passwords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    site_name VARCHAR(255) NOT NULL,
    site_url TEXT,
    username VARCHAR(255),
    password_encrypted TEXT NOT NULL,
    encryption_key_id UUID,
    notes TEXT,
    category VARCHAR(50),
    is_favorite BOOLEAN DEFAULT false,
    strength_score INTEGER CHECK (strength_score >= 0 AND strength_score <= 100),
    last_used_at TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Password analysis history
CREATE TABLE password_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    password_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for tracking without storing password
    strength_score INTEGER NOT NULL CHECK (strength_score >= 0 AND strength_score <= 100),
    strength_level VARCHAR(20) NOT NULL CHECK (strength_level IN ('very_weak', 'weak', 'fair', 'good', 'strong', 'very_strong')),
    entropy_bits DECIMAL(5,2),
    character_sets_used INTEGER,
    length INTEGER NOT NULL,
    has_common_patterns BOOLEAN DEFAULT false,
    time_to_crack_estimate VARCHAR(100),
    issues TEXT[],
    suggestions TEXT[],
    analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Compromised passwords database
CREATE TABLE compromised_passwords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    password_hash VARCHAR(64) UNIQUE NOT NULL,
    breach_count INTEGER DEFAULT 1,
    first_seen_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    sources TEXT[]
);

-- Password breach checks
CREATE TABLE password_breach_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    password_hash VARCHAR(64) NOT NULL,
    is_compromised BOOLEAN NOT NULL,
    breach_count INTEGER DEFAULT 0,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(50) DEFAULT 'haveibeenpwned'
);

-- =====================================================
-- SECURITY MONITORING & ALERTS
-- =====================================================

-- Security alerts
CREATE TABLE security_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    source_type VARCHAR(50),
    source_id UUID,
    metadata JSONB,
    status VARCHAR(20) DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'acknowledged', 'resolved', 'dismissed')),
    action_required BOOLEAN DEFAULT false,
    recommendations TEXT[],
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Alert notifications (email, SMS, push)
CREATE TABLE alert_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alert_id UUID NOT NULL REFERENCES security_alerts(id) ON DELETE CASCADE,
    notification_type VARCHAR(20) NOT NULL CHECK (notification_type IN ('email', 'sms', 'push', 'webhook')),
    recipient VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Security monitoring scans
CREATE TABLE security_scans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    scan_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50),
    target_id UUID,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    results JSONB,
    findings_count INTEGER DEFAULT 0,
    error_message TEXT
);

-- =====================================================
-- USER PREFERENCES & SETTINGS
-- =====================================================

-- User notification preferences
CREATE TABLE user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in_app')),
    is_enabled BOOLEAN DEFAULT true,
    frequency VARCHAR(20) DEFAULT 'immediate' CHECK (frequency IN ('immediate', 'hourly', 'daily', 'weekly')),
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, notification_type, channel)
);

-- User security settings
CREATE TABLE user_security_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, setting_key)
);

-- User privacy settings
CREATE TABLE user_privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    data_retention_period INTEGER DEFAULT 730, -- days
    allow_analytics BOOLEAN DEFAULT false,
    allow_marketing_emails BOOLEAN DEFAULT false,
    allow_third_party_sharing BOOLEAN DEFAULT false,
    data_export_format VARCHAR(20) DEFAULT 'json',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- SECURITY EDUCATION & TIPS
-- =====================================================

-- Security tips and educational content
CREATE TABLE security_tips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    subcategory VARCHAR(50),
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    tags TEXT[],
    read_time_minutes INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User interaction with security tips
CREATE TABLE user_security_tips (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    security_tip_id UUID NOT NULL REFERENCES security_tips(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'bookmarked', 'dismissed')),
    read_at TIMESTAMP WITH TIME ZONE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, security_tip_id)
);

-- =====================================================
-- AUDIT & LOGGING
-- =====================================================

-- Audit log for all user actions
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    ip_address INET,
    user_agent TEXT,
    request_data JSONB,
    response_data JSONB,
    status_code INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- System events and errors
CREATE TABLE system_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    message TEXT NOT NULL,
    component VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ANALYTICS & REPORTING
-- =====================================================

-- User security scores and metrics
CREATE TABLE user_security_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    overall_score INTEGER CHECK (overall_score >= 0 AND overall_score <= 100),
    identity_score INTEGER CHECK (identity_score >= 0 AND identity_score <= 100),
    password_score INTEGER CHECK (password_score >= 0 AND password_score <= 100),
    behavior_score INTEGER CHECK (behavior_score >= 0 AND behavior_score <= 100),
    factors JSONB,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Security metrics aggregation
CREATE TABLE security_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    dimensions JSONB,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(metric_type, metric_name, period_start, period_end)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Sessions indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);

-- Monitored identities indexes
CREATE INDEX idx_monitored_identities_user_id ON monitored_identities(user_id);
CREATE INDEX idx_monitored_identities_type ON monitored_identities(identity_type);
CREATE INDEX idx_monitored_identities_value ON monitored_identities(identity_value);
CREATE INDEX idx_monitored_identities_active ON monitored_identities(is_active);

-- Breaches indexes
CREATE INDEX idx_data_breaches_date ON data_breaches(breach_date);
CREATE INDEX idx_data_breaches_severity ON data_breaches(severity);
CREATE INDEX idx_identity_breaches_identity_id ON identity_breaches(monitored_identity_id);
CREATE INDEX idx_identity_breaches_breach_id ON identity_breaches(data_breach_id);

-- Alerts indexes
CREATE INDEX idx_security_alerts_user_id ON security_alerts(user_id);
CREATE INDEX idx_security_alerts_type ON security_alerts(alert_type);
CREATE INDEX idx_security_alerts_severity ON security_alerts(severity);
CREATE INDEX idx_security_alerts_status ON security_alerts(status);
CREATE INDEX idx_security_alerts_created_at ON security_alerts(created_at);

-- Password indexes
CREATE INDEX idx_user_passwords_user_id ON user_passwords(user_id);
CREATE INDEX idx_password_analyses_user_id ON password_analyses(user_id);
CREATE INDEX idx_password_analyses_hash ON password_analyses(password_hash);
CREATE INDEX idx_compromised_passwords_hash ON compromised_passwords(password_hash);

-- Audit indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitored_identities_updated_at BEFORE UPDATE ON monitored_identities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_breaches_updated_at BEFORE UPDATE ON data_breaches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_passwords_updated_at BEFORE UPDATE ON user_passwords
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_alerts_updated_at BEFORE UPDATE ON security_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_2fa_updated_at BEFORE UPDATE ON user_2fa
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_notification_preferences_updated_at BEFORE UPDATE ON user_notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_security_settings_updated_at BEFORE UPDATE ON user_security_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_privacy_settings_updated_at BEFORE UPDATE ON user_privacy_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_tips_updated_at BEFORE UPDATE ON security_tips
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample security tips
INSERT INTO security_tips (title, content, category, priority, tags, read_time_minutes) VALUES
('Use Strong, Unique Passwords', 'Create complex passwords that are unique for each account. Use a combination of uppercase and lowercase letters, numbers, and special characters. Avoid using personal information or common words.', 'passwords', 'critical', ARRAY['passwords', 'authentication', 'security'], 3),
('Enable Two-Factor Authentication', 'Add an extra layer of security to your accounts by enabling two-factor authentication (2FA). This requires a second form of verification beyond just your password.', 'authentication', 'high', ARRAY['2fa', 'authentication', 'security'], 2),
('Recognize Phishing Attempts', 'Be cautious of emails, texts, or calls requesting personal information. Verify the sender''s identity and never click suspicious links or download unexpected attachments.', 'phishing', 'high', ARRAY['phishing', 'email', 'social engineering'], 4),
('Keep Software Updated', 'Regularly update your operating system, applications, and security software. Enable automatic updates when possible to ensure you have the latest security patches.', 'updates', 'medium', ARRAY['updates', 'patches', 'software'], 2),
('Use a Password Manager', 'Password managers help you generate, store, and manage strong, unique passwords for all your accounts. They encrypt your passwords and only require you to remember one master password.', 'passwords', 'high', ARRAY['password manager', 'passwords', 'tools'], 3);

-- Insert sample data breaches for testing
INSERT INTO data_breaches (name, company, description, breach_date, affected_accounts, data_types, severity) VALUES
('LinkedIn Data Breach', 'LinkedIn', 'Massive data breach exposing user profile information', '2021-06-01', *********, ARRAY['email', 'phone', 'profile_data'], 'high'),
('Facebook Data Leak', 'Facebook', 'Personal data of users leaked through third-party apps', '2021-04-01', *********, ARRAY['email', 'phone', 'personal_info'], 'high'),
('Equifax Breach', 'Equifax', 'Credit reporting agency breach exposing sensitive financial data', '2017-09-01', *********, ARRAY['ssn', 'credit_info', 'personal_info'], 'critical');

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- User security overview
CREATE VIEW user_security_overview AS
SELECT 
    u.id,
    u.email,
    u.first_name,
    u.last_name,
    COUNT(DISTINCT mi.id) as monitored_identities_count,
    COUNT(DISTINCT ib.id) as breach_count,
    COUNT(DISTINCT sa.id) as active_alerts_count,
    COUNT(DISTINCT up.id) as stored_passwords_count,
    uss.overall_score as security_score,
    u.created_at,
    u.last_login_at
FROM users u
LEFT JOIN monitored_identities mi ON u.id = mi.user_id AND mi.is_active = true
LEFT JOIN identity_breaches ib ON mi.id = ib.monitored_identity_id
LEFT JOIN security_alerts sa ON u.id = sa.user_id AND sa.status IN ('unread', 'read')
LEFT JOIN user_passwords up ON u.id = up.user_id
LEFT JOIN user_security_scores uss ON u.id = uss.user_id 
    AND uss.calculated_at = (SELECT MAX(calculated_at) FROM user_security_scores WHERE user_id = u.id)
WHERE u.is_active = true
GROUP BY u.id, u.email, u.first_name, u.last_name, uss.overall_score, u.created_at, u.last_login_at;

-- Recent security activity
CREATE VIEW recent_security_activity AS
SELECT 
    'alert' as activity_type,
    sa.id,
    sa.user_id,
    sa.title as description,
    sa.severity,
    sa.created_at
FROM security_alerts sa
WHERE sa.created_at >= CURRENT_TIMESTAMP - INTERVAL '30 days'
UNION ALL
SELECT 
    'breach' as activity_type,
    ib.id,
    mi.user_id,
    CONCAT('Identity found in ', db.name, ' breach') as description,
    db.severity,
    ib.discovered_at as created_at
FROM identity_breaches ib
JOIN monitored_identities mi ON ib.monitored_identity_id = mi.id
JOIN data_breaches db ON ib.data_breach_id = db.id
WHERE ib.discovered_at >= CURRENT_TIMESTAMP - INTERVAL '30 days'
ORDER BY created_at DESC;

-- =====================================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- =====================================================

-- Function to calculate user security score
CREATE OR REPLACE FUNCTION calculate_user_security_score(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    identity_score INTEGER := 0;
    password_score INTEGER := 0;
    behavior_score INTEGER := 0;
    overall_score INTEGER := 0;
    monitored_count INTEGER;
    breach_count INTEGER;
    strong_password_count INTEGER;
    total_password_count INTEGER;
BEGIN
    -- Calculate identity monitoring score (0-40 points)
    SELECT COUNT(*) INTO monitored_count 
    FROM monitored_identities 
    WHERE user_id = user_uuid AND is_active = true;
    
    SELECT COUNT(DISTINCT ib.id) INTO breach_count
    FROM identity_breaches ib
    JOIN monitored_identities mi ON ib.monitored_identity_id = mi.id
    WHERE mi.user_id = user_uuid;
    
    identity_score := LEAST(40, monitored_count * 10);
    identity_score := identity_score - (breach_count * 5);
    identity_score := GREATEST(0, identity_score);
    
    -- Calculate password score (0-40 points)
    SELECT COUNT(*) INTO total_password_count
    FROM user_passwords
    WHERE user_id = user_uuid;
    
    SELECT COUNT(*) INTO strong_password_count
    FROM user_passwords
    WHERE user_id = user_uuid AND strength_score >= 80;
    
    IF total_password_count > 0 THEN
        password_score := (strong_password_count * 40) / total_password_count;
    END IF;
    
    -- Calculate behavior score (0-20 points)
    behavior_score := 20; -- Base score
    
    -- Deduct points for unread critical alerts
    SELECT COUNT(*) INTO breach_count
    FROM security_alerts
    WHERE user_id = user_uuid 
    AND severity = 'critical' 
    AND status = 'unread';
    
    behavior_score := behavior_score - (breach_count * 5);
    behavior_score := GREATEST(0, behavior_score);
    
    -- Calculate overall score
    overall_score := identity_score + password_score + behavior_score;
    overall_score := LEAST(100, overall_score);
    
    -- Insert the calculated score
    INSERT INTO user_security_scores (
        user_id, 
        overall_score, 
        identity_score, 
        password_score, 
        behavior_score,
        factors
    ) VALUES (
        user_uuid,
        overall_score,
        identity_score,
        password_score,
        behavior_score,
        jsonb_build_object(
            'monitored_identities', monitored_count,
            'breaches', breach_count,
            'strong_passwords', strong_password_count,
            'total_passwords', total_password_count
        )
    );
    
    RETURN overall_score;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ROLES AND PERMISSIONS
-- =====================================================

-- Create application roles
CREATE ROLE authenticated_users;
CREATE ROLE admin_users;

-- =====================================================
-- SECURITY POLICIES (Row Level Security)
-- =====================================================

-- Enable RLS on sensitive tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE monitored_identities ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_passwords ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY user_isolation_policy ON users
    FOR ALL TO authenticated_users
    USING (id = current_setting('app.current_user_id')::UUID);

CREATE POLICY user_identities_policy ON monitored_identities
    FOR ALL TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

CREATE POLICY user_passwords_policy ON user_passwords
    FOR ALL TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

CREATE POLICY user_alerts_policy ON security_alerts
    FOR ALL TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

CREATE POLICY user_sessions_policy ON user_sessions
    FOR ALL TO authenticated_users
    USING (user_id = current_setting('app.current_user_id')::UUID);

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON users TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON monitored_identities TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_passwords TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON security_alerts TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_sessions TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_2fa TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_notification_preferences TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_security_settings TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_privacy_settings TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_security_tips TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON password_analyses TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON password_breach_checks TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON audit_logs TO authenticated_users;

-- Read-only access to reference data
GRANT SELECT ON data_breaches TO authenticated_users;
GRANT SELECT ON security_tips TO authenticated_users;
GRANT SELECT ON compromised_passwords TO authenticated_users;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated_users;

-- Admin users get full access
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO admin_users;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO admin_users;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON DATABASE theguardian IS 'Identity Guardian cybersecurity application database';

COMMENT ON TABLE users IS 'Core user accounts with authentication and profile information';
COMMENT ON TABLE user_sessions IS 'Active user sessions for authentication tracking';
COMMENT ON TABLE user_2fa IS 'Two-factor authentication settings for users';
COMMENT ON TABLE monitored_identities IS 'User identities being monitored for breaches';
COMMENT ON TABLE data_breaches IS 'Database of known data breaches';
COMMENT ON TABLE identity_breaches IS 'Associations between monitored identities and breaches';
COMMENT ON TABLE dark_web_findings IS 'Results from dark web monitoring scans';
COMMENT ON TABLE user_passwords IS 'Encrypted password vault for users';
COMMENT ON TABLE password_analyses IS 'History of password strength analyses';
COMMENT ON TABLE compromised_passwords IS 'Database of known compromised passwords';
COMMENT ON TABLE security_alerts IS 'Security alerts and notifications for users';
COMMENT ON TABLE security_tips IS 'Educational security tips and best practices';
COMMENT ON TABLE audit_logs IS 'Audit trail of all user actions';
COMMENT ON TABLE user_security_scores IS 'Calculated security scores for users';

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Procedure to clean up old sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    INSERT INTO system_events (event_type, severity, message, component)
    VALUES ('cleanup', 'info', 
            'Cleaned up ' || deleted_count || ' expired sessions', 
            'session_cleanup');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Procedure to archive old audit logs
CREATE OR REPLACE FUNCTION archive_old_audit_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER;
BEGIN
    -- In a real implementation, you might move to an archive table
    DELETE FROM audit_logs 
    WHERE created_at < CURRENT_TIMESTAMP - (days_to_keep || ' days')::INTERVAL;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    INSERT INTO system_events (event_type, severity, message, component)
    VALUES ('archive', 'info', 
            'Archived ' || archived_count || ' old audit log entries', 
            'audit_cleanup');
    
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Identity Guardian database schema created successfully!';
    RAISE NOTICE 'Database: theguardian';
    RAISE NOTICE 'Tables created: %', (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public');
    RAISE NOTICE 'Indexes created: %', (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public');
    RAISE NOTICE 'Functions created: %', (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public');
END $$;


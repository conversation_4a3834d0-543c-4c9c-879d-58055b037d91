import {
    Activity,
    AlertTriangle,
    Award,
    BarChart3,
    Bell,
    CheckCircle,
    Clock,
    Eye,
    Globe,
    Key,
    Lock,
    Settings,
    Shield,
    Smartphone,
    Target,
    TrendingUp,
    Users,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';

const DashboardPage = () => {
  const [securityScore, setSecurityScore] = useState(85);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000);
  }, []);

  const securityMetrics = [
    {
      title: 'Security Score',
      value: securityScore,
      max: 100,
      status: 'excellent',
      icon: Shield,
      gradient: 'from-green-500 to-emerald-500',
      description: 'Overall security health of your digital identity'
    },
    {
      title: 'Identities Monitored',
      value: 5,
      status: 'active',
      icon: Eye,
      gradient: 'from-blue-500 to-cyan-500',
      description: 'Phone numbers & emails'
    },
    {
      title: 'Breaches Detected',
      value: 2,
      status: 'warning',
      icon: AlertTriangle,
      gradient: 'from-orange-500 to-red-500',
      description: 'Requires attention'
    },
    {
      title: 'Strong Passwords',
      value: 8,
      total: 10,
      status: 'good',
      icon: Lock,
      gradient: 'from-purple-500 to-pink-500',
      description: 'Password vault status'
    }
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'breach',
      title: 'New breach detected',
      description: 'Your email found in LinkedIn data breach',
      time: '2 hours ago',
      severity: 'high',
      icon: AlertTriangle,
      color: 'text-red-400'
    },
    {
      id: 2,
      type: 'password',
      title: 'Weak password detected',
      description: 'Password for social media account needs strengthening',
      time: '1 day ago',
      severity: 'medium',
      icon: Key,
      color: 'text-yellow-400'
    },
    {
      id: 3,
      type: 'monitoring',
      title: 'New identity added',
      description: 'Started monitoring ******-0123',
      time: '3 days ago',
      severity: 'info',
      icon: Smartphone,
      color: 'text-blue-400'
    },
    {
      id: 4,
      type: 'security',
      title: 'Security score improved',
      description: 'Your security score increased to 85/100',
      time: '1 week ago',
      severity: 'success',
      icon: TrendingUp,
      color: 'text-green-400'
    }
  ];

  const quickActions = [
    {
      title: 'Add Identity',
      description: 'Monitor new phone or email',
      icon: Eye,
      gradient: 'from-blue-500 to-cyan-500',
      action: () => console.log('Add identity')
    },
    {
      title: 'Check Password',
      description: 'Analyze password strength',
      icon: Lock,
      gradient: 'from-purple-500 to-pink-500',
      action: () => console.log('Check password')
    },
    {
      title: 'Security Tips',
      description: 'Learn best practices',
      icon: Target,
      gradient: 'from-green-500 to-emerald-500',
      action: () => console.log('Security tips')
    },
    {
      title: 'Generate Password',
      description: 'Create secure password',
      icon: Key,
      gradient: 'from-orange-500 to-red-500',
      action: () => console.log('Generate password')
    }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen animated-bg flex items-center justify-center">
        <div className="glass-card p-8 text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading your security dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-dark">
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className="mb-10 slide-in">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-8">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center glow-primary">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-5xl font-bold text-white mb-2">
                    Welcome back, <span className="text-cyber-primary">Demo!</span>
                  </h1>
                  <p className="text-slate-300 text-xl">Your digital security command center</p>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center space-x-2 bg-gradient-glass border border-cyber-accent-green/30 rounded-full px-4 py-2">
                  <div className="w-2 h-2 bg-cyber-accent-green rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-cyber-accent-green">All Systems Secure</span>
                </div>
                <div className="flex items-center space-x-2 text-slate-400 text-sm">
                  <Clock className="h-4 w-4" />
                  <span>Last scan: 2 minutes ago</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button className="btn-secondary flex items-center space-x-2 hover:glow-secondary transition-all duration-300">
                <Bell className="h-5 w-5" />
                <span className="hidden sm:inline">Alerts</span>
                <div className="w-2 h-2 bg-cyber-accent-red rounded-full animate-pulse"></div>
              </button>
              <button className="btn-primary flex items-center space-x-2 glow-primary">
                <Settings className="h-5 w-5" />
                <span className="hidden sm:inline">Settings</span>
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Security Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-12">
          {securityMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <div key={index} className="cyber-card p-8 hover:glow-primary transition-all duration-500 group relative overflow-hidden">
                {/* Background Animation */}
                <div className="absolute inset-0 bg-gradient-secondary opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:glow-secondary transition-all duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex items-center space-x-2">
                      {metric.status === 'excellent' && (
                        <div className="flex items-center space-x-1">
                          <Award className="h-5 w-5 text-cyber-accent-yellow" />
                          <span className="text-xs text-cyber-accent-yellow font-medium">Excellent</span>
                        </div>
                      )}
                      {metric.status === 'warning' && (
                        <div className="flex items-center space-x-1">
                          <AlertTriangle className="h-5 w-5 text-cyber-accent-red" />
                          <span className="text-xs text-cyber-accent-red font-medium">Warning</span>
                        </div>
                      )}
                      {metric.status === 'active' && (
                        <div className="flex items-center space-x-1">
                          <div className="w-2 h-2 bg-cyber-accent-green rounded-full animate-pulse"></div>
                          <span className="text-xs text-cyber-accent-green font-medium">Active</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="mb-6">
                    <div className="flex items-baseline space-x-2 mb-2">
                      <span className="text-4xl font-bold text-white text-gradient">
                        {metric.value}
                      </span>
                      {metric.max && (
                        <span className="text-slate-400 text-lg">/{metric.max}</span>
                      )}
                      {metric.total && (
                        <span className="text-slate-400 text-lg">/{metric.total}</span>
                      )}
                    </div>
                    <h3 className="text-slate-300 font-semibold text-lg group-hover:text-white transition-colors duration-300">
                      {metric.title}
                    </h3>
                  </div>

                  {/* Progress Bar */}
                  {metric.max && (
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-xs text-slate-400">
                        <span>Progress</span>
                        <span>{Math.round((metric.value / metric.max) * 100)}%</span>
                      </div>
                      <div className="w-full bg-slate-700/50 rounded-full h-3 overflow-hidden">
                        <div
                          className="bg-gradient-secondary h-3 rounded-full transition-all duration-1000 relative overflow-hidden"
                          style={{ width: `${(metric.value / metric.max) * 100}%` }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Description */}
                  <p className="text-slate-400 text-sm leading-relaxed group-hover:text-slate-300 transition-colors duration-300">
                    {metric.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Enhanced Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Enhanced Recent Activity */}
          <div className="lg:col-span-2">
            <div className="cyber-card p-8 hover:glow-primary transition-all duration-500">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-secondary rounded-2xl flex items-center justify-center">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">Security Activity</h2>
                    <p className="text-slate-400 text-sm">Real-time monitoring events</p>
                  </div>
                </div>
                <button className="btn-secondary text-sm hover:glow-secondary transition-all duration-300">
                  View All Events
                </button>
              </div>

              <div className="space-y-4">
                {recentActivity.map((activity) => {
                  const Icon = activity.icon;
                  return (
                    <div key={activity.id} className="group flex items-start space-x-4 p-6 rounded-2xl bg-gradient-glass border border-slate-700/50 hover:border-cyber-primary/30 hover:bg-slate-800/30 transition-all duration-300">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 ${
                        activity.severity === 'high' ? 'bg-gradient-danger glow-danger' :
                        activity.severity === 'medium' ? 'bg-gradient-warning glow-warning' :
                        activity.severity === 'success' ? 'bg-gradient-success glow-success' :
                        'bg-gradient-secondary glow-primary'
                      }`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>

                      <div className="flex-1">
                        <h3 className="text-white font-semibold mb-2 group-hover:text-cyber-primary transition-colors duration-300">
                          {activity.title}
                        </h3>
                        <p className="text-slate-400 text-sm mb-3 leading-relaxed group-hover:text-slate-300 transition-colors duration-300">
                          {activity.description}
                        </p>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-slate-500" />
                            <span className="text-slate-500 text-xs font-medium">{activity.time}</span>
                          </div>
                          <div className="w-1 h-1 bg-slate-600 rounded-full"></div>
                          <span className="text-xs text-slate-500">Security Event</span>
                        </div>
                      </div>

                      <div className={`px-4 py-2 rounded-full text-xs font-semibold border transition-all duration-300 ${
                        activity.severity === 'high' ? 'bg-cyber-accent-red/10 text-cyber-accent-red border-cyber-accent-red/30' :
                        activity.severity === 'medium' ? 'bg-cyber-accent-yellow/10 text-cyber-accent-yellow border-cyber-accent-yellow/30' :
                        activity.severity === 'success' ? 'bg-cyber-accent-green/10 text-cyber-accent-green border-cyber-accent-green/30' :
                        'bg-cyber-primary/10 text-cyber-primary border-cyber-primary/30'
                      }`}>
                        {activity.severity.toUpperCase()}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="space-y-8">
            {/* Quick Actions Card */}
            <div className="cyber-card p-8 hover:glow-secondary transition-all duration-500">
              <div className="flex items-center space-x-4 mb-8">
                <div className="w-12 h-12 bg-gradient-warning rounded-2xl flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Quick Actions</h2>
                  <p className="text-slate-400 text-sm">Instant security controls</p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <button
                      key={index}
                      onClick={action.action}
                      className="group p-6 rounded-2xl bg-gradient-glass border border-slate-700/50 hover:border-cyber-primary/30 hover:bg-slate-800/30 transition-all duration-300 text-left"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-secondary rounded-xl flex items-center justify-center group-hover:scale-110 group-hover:glow-secondary transition-all duration-300">
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-white font-semibold mb-1 group-hover:text-cyber-primary transition-colors duration-300">
                            {action.title}
                          </h3>
                          <p className="text-slate-400 text-sm group-hover:text-slate-300 transition-colors duration-300">
                            {action.description}
                          </p>
                        </div>
                        <div className="w-6 h-6 rounded-full bg-cyber-primary/20 flex items-center justify-center group-hover:bg-cyber-primary/30 transition-colors duration-300">
                          <div className="w-2 h-2 bg-cyber-primary rounded-full"></div>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Enhanced Security Status */}
            <div className="cyber-card p-8 hover:glow-success transition-all duration-500">
              <div className="flex items-center space-x-4 mb-8">
                <div className="w-12 h-12 bg-gradient-success rounded-2xl flex items-center justify-center glow-success">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Security Status</h2>
                  <p className="text-slate-400 text-sm">System health overview</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 rounded-2xl bg-gradient-success/10 border border-cyber-accent-green/20">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-success rounded-xl flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-white font-medium">Protection Active</span>
                      <p className="text-cyber-accent-green text-xs">All systems operational</p>
                    </div>
                  </div>
                  <div className="px-3 py-1 bg-cyber-accent-green/20 text-cyber-accent-green rounded-full text-xs font-semibold">
                    SECURE
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 rounded-2xl bg-gradient-secondary/10 border border-cyber-primary/20">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-secondary rounded-xl flex items-center justify-center">
                      <Eye className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-white font-medium">Monitoring</span>
                      <p className="text-cyber-primary text-xs">Real-time surveillance</p>
                    </div>
                  </div>
                  <div className="px-3 py-1 bg-cyber-primary/20 text-cyber-primary rounded-full text-xs font-semibold">
                    24/7
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 rounded-2xl bg-gradient-warning/10 border border-cyber-accent-yellow/20">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-warning rounded-xl flex items-center justify-center">
                      <AlertTriangle className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-white font-medium">Security Alerts</span>
                      <p className="text-cyber-accent-yellow text-xs">Requires attention</p>
                    </div>
                  </div>
                  <div className="px-3 py-1 bg-cyber-accent-yellow/20 text-cyber-accent-yellow rounded-full text-xs font-semibold">
                    2 NEW
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Security Score Chart */}
            <div className="cyber-card p-8 hover:glow-primary transition-all duration-500">
              <div className="flex items-center space-x-4 mb-8">
                <div className="w-12 h-12 bg-gradient-secondary rounded-2xl flex items-center justify-center">
                  <BarChart3 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Security Breakdown</h2>
                  <p className="text-slate-400 text-sm">Detailed score analysis</p>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-slate-300 font-medium">Identity Security</span>
                    <span className="text-white font-bold text-lg">90%</span>
                  </div>
                  <div className="w-full bg-slate-700/50 rounded-full h-4 overflow-hidden">
                    <div className="bg-gradient-success h-4 rounded-full transition-all duration-1000 relative overflow-hidden" style={{ width: '90%' }}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-slate-300 font-medium">Password Strength</span>
                    <span className="text-white font-bold text-lg">85%</span>
                  </div>
                  <div className="w-full bg-slate-700/50 rounded-full h-4 overflow-hidden">
                    <div className="bg-gradient-secondary h-4 rounded-full transition-all duration-1000 relative overflow-hidden" style={{ width: '85%' }}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-slate-300 font-medium">Behavior Score</span>
                    <span className="text-white font-bold text-lg">80%</span>
                  </div>
                  <div className="w-full bg-slate-700/50 rounded-full h-4 overflow-hidden">
                    <div className="bg-gradient-warning h-4 rounded-full transition-all duration-1000 relative overflow-hidden" style={{ width: '80%' }}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Bottom Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="cyber-card p-8 text-center hover:glow-primary transition-all duration-500 group">
            <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:glow-secondary transition-all duration-300">
              <Globe className="h-8 w-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-white mb-3 text-gradient">Global</div>
            <div className="text-slate-300 font-medium mb-2">Worldwide Coverage</div>
            <div className="text-slate-400 text-sm leading-relaxed">
              Comprehensive monitoring across all major regions and threat landscapes
            </div>
            <div className="mt-4 h-1 bg-gradient-secondary rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          <div className="cyber-card p-8 text-center hover:glow-success transition-all duration-500 group">
            <div className="w-16 h-16 bg-gradient-success rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:glow-success transition-all duration-300">
              <Users className="h-8 w-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-white mb-3 text-gradient">50M+</div>
            <div className="text-slate-300 font-medium mb-2">Protected Identities</div>
            <div className="text-slate-400 text-sm leading-relaxed">
              Millions of digital identities secured through our advanced monitoring platform
            </div>
            <div className="mt-4 h-1 bg-gradient-success rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          <div className="cyber-card p-8 text-center hover:glow-warning transition-all duration-500 group">
            <div className="w-16 h-16 bg-gradient-warning rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:glow-warning transition-all duration-300">
              <Zap className="h-8 w-8 text-white" />
            </div>
            <div className="text-3xl font-bold text-white mb-3 text-gradient">Real-time</div>
            <div className="text-slate-300 font-medium mb-2">Threat Detection</div>
            <div className="text-slate-400 text-sm leading-relaxed">
              Instant alerts and automated response to emerging cybersecurity threats
            </div>
            <div className="mt-4 h-1 bg-gradient-warning rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;


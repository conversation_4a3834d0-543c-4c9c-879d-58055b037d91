import React, { useState } from 'react';
import Layout from '../components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Switch } from '../components/ui/switch';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription } from '../components/ui/alert';
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  Key, 
  Download,
  Trash2,
  Save,
  AlertTriangle,
  CheckCircle,
  Mail,
  Smartphone,
  Globe
} from 'lucide-react';
import { toast } from 'sonner';

const SettingsPage = () => {
  const [profile, setProfile] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    timezone: 'America/New_York'
  });

  const [notifications, setNotifications] = useState({
    emailAlerts: true,
    smsAlerts: true,
    pushNotifications: false,
    weeklyReports: true,
    securityTips: true,
    breachAlerts: true,
    suspiciousActivity: true
  });

  const [privacy, setPrivacy] = useState({
    dataRetention: '2-years',
    shareAnonymousData: false,
    marketingEmails: false,
    thirdPartySharing: false
  });

  const [security, setSecurity] = useState({
    twoFactorEnabled: true,
    sessionTimeout: '30-minutes',
    loginNotifications: true,
    deviceTracking: true
  });

  const handleProfileSave = () => {
    toast.success('Profile updated successfully!');
  };

  const handleNotificationSave = () => {
    toast.success('Notification preferences saved!');
  };

  const handlePrivacySave = () => {
    toast.success('Privacy settings updated!');
  };

  const handleSecuritySave = () => {
    toast.success('Security settings updated!');
  };

  const handleExportData = () => {
    toast.success('Data export initiated. You will receive an email when ready.');
  };

  const handleDeleteAccount = () => {
    toast.error('Account deletion requires additional verification. Please contact support.');
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Account Settings</h1>
            <p className="text-slate-400">
              Manage your account preferences, security settings, and privacy controls
            </p>
          </div>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-slate-800/50">
            <TabsTrigger value="profile" className="data-[state=active]:bg-purple-600">
              <User className="w-4 h-4 mr-2" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="notifications" className="data-[state=active]:bg-purple-600">
              <Bell className="w-4 h-4 mr-2" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="security" className="data-[state=active]:bg-purple-600">
              <Shield className="w-4 h-4 mr-2" />
              Security
            </TabsTrigger>
            <TabsTrigger value="privacy" className="data-[state=active]:bg-purple-600">
              <Key className="w-4 h-4 mr-2" />
              Privacy
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Personal Information</CardTitle>
                <CardDescription>Update your personal details and contact information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-slate-300">First Name</Label>
                    <Input
                      id="firstName"
                      value={profile.firstName}
                      onChange={(e) => setProfile({...profile, firstName: e.target.value})}
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-slate-300">Last Name</Label>
                    <Input
                      id="lastName"
                      value={profile.lastName}
                      onChange={(e) => setProfile({...profile, lastName: e.target.value})}
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-slate-300">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({...profile, email: e.target.value})}
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-slate-300">Phone Number</Label>
                  <Input
                    id="phone"
                    value={profile.phone}
                    onChange={(e) => setProfile({...profile, phone: e.target.value})}
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timezone" className="text-slate-300">Timezone</Label>
                  <select
                    id="timezone"
                    value={profile.timezone}
                    onChange={(e) => setProfile({...profile, timezone: e.target.value})}
                    className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded-md text-white"
                  >
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    <option value="UTC">UTC</option>
                  </select>
                </div>

                <Button onClick={handleProfileSave} className="bg-purple-600 hover:bg-purple-700">
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Account Actions</CardTitle>
                <CardDescription>Export your data or delete your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                  <div>
                    <h4 className="text-white font-medium">Export Account Data</h4>
                    <p className="text-slate-400 text-sm">Download all your data in a portable format</p>
                  </div>
                  <Button onClick={handleExportData} variant="outline" className="border-slate-600 text-slate-300">
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 bg-red-500/10 rounded-lg border border-red-500/50">
                  <div>
                    <h4 className="text-white font-medium">Delete Account</h4>
                    <p className="text-red-300 text-sm">Permanently delete your account and all data</p>
                  </div>
                  <Button onClick={handleDeleteAccount} variant="destructive" className="bg-red-600 hover:bg-red-700">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Notification Preferences</CardTitle>
                <CardDescription>Choose how you want to receive alerts and updates</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="text-white font-medium">Delivery Methods</h4>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-blue-400" />
                      <div>
                        <p className="text-white font-medium">Email Alerts</p>
                        <p className="text-slate-400 text-sm">Receive security alerts via email</p>
                      </div>
                    </div>
                    <Switch
                      checked={notifications.emailAlerts}
                      onCheckedChange={(checked) => setNotifications({...notifications, emailAlerts: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Smartphone className="h-5 w-5 text-green-400" />
                      <div>
                        <p className="text-white font-medium">SMS Alerts</p>
                        <p className="text-slate-400 text-sm">Critical alerts via text message</p>
                      </div>
                    </div>
                    <Switch
                      checked={notifications.smsAlerts}
                      onCheckedChange={(checked) => setNotifications({...notifications, smsAlerts: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Globe className="h-5 w-5 text-purple-400" />
                      <div>
                        <p className="text-white font-medium">Push Notifications</p>
                        <p className="text-slate-400 text-sm">Browser notifications</p>
                      </div>
                    </div>
                    <Switch
                      checked={notifications.pushNotifications}
                      onCheckedChange={(checked) => setNotifications({...notifications, pushNotifications: checked})}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-white font-medium">Alert Types</h4>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Data Breach Alerts</p>
                      <p className="text-slate-400 text-sm">Immediate notification of identity breaches</p>
                    </div>
                    <Switch
                      checked={notifications.breachAlerts}
                      onCheckedChange={(checked) => setNotifications({...notifications, breachAlerts: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Suspicious Activity</p>
                      <p className="text-slate-400 text-sm">Alerts for unusual account activity</p>
                    </div>
                    <Switch
                      checked={notifications.suspiciousActivity}
                      onCheckedChange={(checked) => setNotifications({...notifications, suspiciousActivity: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Weekly Reports</p>
                      <p className="text-slate-400 text-sm">Summary of your security status</p>
                    </div>
                    <Switch
                      checked={notifications.weeklyReports}
                      onCheckedChange={(checked) => setNotifications({...notifications, weeklyReports: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Security Tips</p>
                      <p className="text-slate-400 text-sm">Educational content and best practices</p>
                    </div>
                    <Switch
                      checked={notifications.securityTips}
                      onCheckedChange={(checked) => setNotifications({...notifications, securityTips: checked})}
                    />
                  </div>
                </div>

                <Button onClick={handleNotificationSave} className="bg-purple-600 hover:bg-purple-700">
                  <Save className="w-4 h-4 mr-2" />
                  Save Preferences
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Security Settings</CardTitle>
                <CardDescription>Manage your account security and authentication</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-green-500/10 rounded-lg border border-green-500/50">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-6 w-6 text-green-400" />
                    <div>
                      <p className="text-white font-medium">Two-Factor Authentication</p>
                      <p className="text-green-300 text-sm">Enabled with authenticator app</p>
                    </div>
                  </div>
                  <Button variant="outline" className="border-green-500/50 text-green-300">
                    Manage
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Login Notifications</p>
                      <p className="text-slate-400 text-sm">Get notified of new device logins</p>
                    </div>
                    <Switch
                      checked={security.loginNotifications}
                      onCheckedChange={(checked) => setSecurity({...security, loginNotifications: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Device Tracking</p>
                      <p className="text-slate-400 text-sm">Monitor devices accessing your account</p>
                    </div>
                    <Switch
                      checked={security.deviceTracking}
                      onCheckedChange={(checked) => setSecurity({...security, deviceTracking: checked})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-white font-medium">Session Timeout</Label>
                    <select
                      value={security.sessionTimeout}
                      onChange={(e) => setSecurity({...security, sessionTimeout: e.target.value})}
                      className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded-md text-white"
                    >
                      <option value="15-minutes">15 minutes</option>
                      <option value="30-minutes">30 minutes</option>
                      <option value="1-hour">1 hour</option>
                      <option value="4-hours">4 hours</option>
                      <option value="never">Never</option>
                    </select>
                  </div>
                </div>

                <Button onClick={handleSecuritySave} className="bg-purple-600 hover:bg-purple-700">
                  <Save className="w-4 h-4 mr-2" />
                  Update Security Settings
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Active Sessions</CardTitle>
                <CardDescription>Manage devices that have access to your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                  <div>
                    <p className="text-white font-medium">Current Session</p>
                    <p className="text-slate-400 text-sm">Chrome on Windows • New York, NY</p>
                    <p className="text-slate-500 text-xs">Active now</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-400" />
                    <span className="text-green-300 text-sm">Current</span>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                  <div>
                    <p className="text-white font-medium">Mobile App</p>
                    <p className="text-slate-400 text-sm">iPhone • New York, NY</p>
                    <p className="text-slate-500 text-xs">Last active 2 hours ago</p>
                  </div>
                  <Button variant="outline" size="sm" className="border-red-500/50 text-red-300">
                    Revoke
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Privacy Tab */}
          <TabsContent value="privacy" className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Privacy Controls</CardTitle>
                <CardDescription>Manage how your data is used and stored</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-white font-medium">Data Retention Period</Label>
                    <select
                      value={privacy.dataRetention}
                      onChange={(e) => setPrivacy({...privacy, dataRetention: e.target.value})}
                      className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded-md text-white"
                    >
                      <option value="1-year">1 year</option>
                      <option value="2-years">2 years</option>
                      <option value="5-years">5 years</option>
                      <option value="indefinite">Indefinite</option>
                    </select>
                    <p className="text-slate-400 text-sm">How long we keep your monitoring data</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Anonymous Analytics</p>
                      <p className="text-slate-400 text-sm">Help improve our service with anonymous usage data</p>
                    </div>
                    <Switch
                      checked={privacy.shareAnonymousData}
                      onCheckedChange={(checked) => setPrivacy({...privacy, shareAnonymousData: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Marketing Communications</p>
                      <p className="text-slate-400 text-sm">Receive product updates and security news</p>
                    </div>
                    <Switch
                      checked={privacy.marketingEmails}
                      onCheckedChange={(checked) => setPrivacy({...privacy, marketingEmails: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Third-Party Data Sharing</p>
                      <p className="text-slate-400 text-sm">Share data with security research partners</p>
                    </div>
                    <Switch
                      checked={privacy.thirdPartySharing}
                      onCheckedChange={(checked) => setPrivacy({...privacy, thirdPartySharing: checked})}
                    />
                  </div>
                </div>

                <Alert className="border-blue-500/50 bg-blue-500/10">
                  <Shield className="h-4 w-4" />
                  <AlertDescription className="text-blue-300">
                    <strong>Privacy Commitment:</strong> We never sell your personal data. All data sharing is optional and anonymized for security research purposes only.
                  </AlertDescription>
                </Alert>

                <Button onClick={handlePrivacySave} className="bg-purple-600 hover:bg-purple-700">
                  <Save className="w-4 h-4 mr-2" />
                  Save Privacy Settings
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Data Rights</CardTitle>
                <CardDescription>Exercise your data protection rights</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                    <h4 className="text-white font-medium mb-2">Right to Access</h4>
                    <p className="text-slate-400 text-sm mb-3">Request a copy of all data we have about you</p>
                    <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                      Request Data
                    </Button>
                  </div>
                  <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                    <h4 className="text-white font-medium mb-2">Right to Rectification</h4>
                    <p className="text-slate-400 text-sm mb-3">Correct any inaccurate personal data</p>
                    <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                      Update Data
                    </Button>
                  </div>
                  <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                    <h4 className="text-white font-medium mb-2">Right to Erasure</h4>
                    <p className="text-slate-400 text-sm mb-3">Request deletion of your personal data</p>
                    <Button variant="outline" size="sm" className="border-red-500/50 text-red-300">
                      Delete Data
                    </Button>
                  </div>
                  <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                    <h4 className="text-white font-medium mb-2">Right to Portability</h4>
                    <p className="text-slate-400 text-sm mb-3">Export your data in a machine-readable format</p>
                    <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                      Export Data
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default SettingsPage;


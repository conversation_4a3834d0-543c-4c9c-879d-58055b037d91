import React from 'react';
import { cn } from '@/lib/utils';

const StatusIndicator = React.forwardRef(({ 
  className, 
  status = 'online',
  size = 'default',
  label,
  showLabel = true,
  animated = true,
  ...props 
}, ref) => {
  const statuses = {
    online: {
      color: 'bg-cyber-accent-green',
      label: 'Online',
      glow: 'shadow-[0_0_10px_rgba(0,255,136,0.5)]'
    },
    offline: {
      color: 'bg-slate-500',
      label: 'Offline',
      glow: ''
    },
    warning: {
      color: 'bg-cyber-accent-yellow',
      label: 'Warning',
      glow: 'shadow-[0_0_10px_rgba(251,191,36,0.5)]'
    },
    error: {
      color: 'bg-cyber-accent-red',
      label: 'Error',
      glow: 'shadow-[0_0_10px_rgba(239,68,68,0.5)]'
    },
    processing: {
      color: 'bg-cyber-primary',
      label: 'Processing',
      glow: 'shadow-[0_0_10px_rgba(0,212,255,0.5)]'
    },
    secure: {
      color: 'bg-gradient-to-r from-cyber-accent-green to-cyber-primary',
      label: 'Secure',
      glow: 'shadow-[0_0_15px_rgba(0,255,136,0.6)]'
    }
  };

  const sizes = {
    sm: 'w-2 h-2',
    default: 'w-3 h-3',
    lg: 'w-4 h-4',
    xl: 'w-6 h-6'
  };

  const currentStatus = statuses[status] || statuses.online;
  const displayLabel = label || currentStatus.label;

  return (
    <div
      className={cn('flex items-center space-x-2', className)}
      ref={ref}
      {...props}
    >
      <div className="relative">
        <div
          className={cn(
            'rounded-full transition-all duration-300',
            sizes[size],
            currentStatus.color,
            animated && 'animate-pulse',
            currentStatus.glow
          )}
        />
        {animated && status === 'processing' && (
          <div
            className={cn(
              'absolute inset-0 rounded-full animate-ping',
              sizes[size],
              currentStatus.color,
              'opacity-75'
            )}
          />
        )}
      </div>
      
      {showLabel && displayLabel && (
        <span className="text-sm font-medium text-slate-300">
          {displayLabel}
        </span>
      )}
    </div>
  );
});

StatusIndicator.displayName = 'StatusIndicator';

const StatusBadge = React.forwardRef(({ 
  className, 
  status = 'online',
  children,
  variant = 'default',
  ...props 
}, ref) => {
  const statuses = {
    online: 'bg-cyber-accent-green/20 text-cyber-accent-green border-cyber-accent-green/30',
    offline: 'bg-slate-500/20 text-slate-400 border-slate-500/30',
    warning: 'bg-cyber-accent-yellow/20 text-cyber-accent-yellow border-cyber-accent-yellow/30',
    error: 'bg-cyber-accent-red/20 text-cyber-accent-red border-cyber-accent-red/30',
    processing: 'bg-cyber-primary/20 text-cyber-primary border-cyber-primary/30',
    secure: 'bg-gradient-success/20 text-cyber-accent-green border-cyber-accent-green/30'
  };

  const variants = {
    default: 'px-3 py-1 rounded-full text-xs font-semibold border transition-all duration-300',
    pill: 'px-4 py-2 rounded-full text-sm font-medium border transition-all duration-300',
    compact: 'px-2 py-1 rounded text-xs font-medium border transition-all duration-300'
  };

  const currentStatus = statuses[status] || statuses.online;

  return (
    <div
      className={cn(
        variants[variant],
        currentStatus,
        'hover:scale-105',
        className
      )}
      ref={ref}
      {...props}
    >
      <div className="flex items-center space-x-2">
        <StatusIndicator 
          status={status} 
          size="sm" 
          showLabel={false} 
          animated={status === 'processing' || status === 'secure'}
        />
        <span>{children}</span>
      </div>
    </div>
  );
});

StatusBadge.displayName = 'StatusBadge';

export { StatusIndicator, StatusBadge };

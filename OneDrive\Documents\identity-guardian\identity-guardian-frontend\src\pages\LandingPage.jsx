import {
    <PERSON><PERSON><PERSON>,
    Award,
    Brain,
    CheckCircle,
    Eye,
    Globe,
    Lock,
    Shield,
    Target,
    TrendingUp,
    Users,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

const LandingPage = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentStat, setCurrentStat] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentStat(prev => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    { number: '50M+', label: 'Breaches Monitored', icon: Shield },
    { number: '99.9%', label: 'Uptime Guarantee', icon: TrendingUp },
    { number: '24/7', label: 'Real-time Monitoring', icon: Eye },
    { number: '256-bit', label: 'Encryption Standard', icon: Lock }
  ];

  const features = [
    {
      icon: Eye,
      title: 'Identity Monitoring',
      description: 'Monitor your phone numbers and email addresses for breaches and unauthorized use across the dark web and breach databases.',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Lock,
      title: 'Password Security',
      description: 'Analyze password strength, detect compromised passwords, and generate cryptographically secure passwords and passphrases.',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: Zap,
      title: 'Real-time Alerts',
      description: 'Get instant notifications via email and SMS when your identities are found in new breaches or suspicious activities.',
      gradient: 'from-orange-500 to-red-500'
    },
    {
      icon: Brain,
      title: 'Security Education',
      description: 'Learn cybersecurity best practices with personalized tips, phishing protection guides, and threat awareness content.',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      icon: Globe,
      title: 'Global Coverage',
      description: 'Monitor identities worldwide with support for international phone numbers and comprehensive breach database coverage.',
      gradient: 'from-indigo-500 to-purple-500'
    },
    {
      icon: Target,
      title: 'Advanced Analytics',
      description: 'Get detailed risk assessments, trend analysis, and actionable insights to improve your security posture.',
      gradient: 'from-pink-500 to-rose-500'
    }
  ];

  const securityTips = [
    'Never reuse passwords across multiple accounts',
    'Enable two-factor authentication on all important accounts',
    'Be suspicious of urgent emails requesting personal information',
    'Keep your software and devices updated with latest security patches',
    'Use a password manager to generate and store strong passwords'
  ];

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Enhanced Navigation */}
      <nav className="fixed top-0 w-full z-50 cyber-card border-0 border-b border-cyber-primary/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-secondary rounded-xl flex items-center justify-center glow-primary">
                  <Shield className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-cyber-accent-green rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Identity Guardian</h1>
                <div className="flex items-center space-x-2">
                  <span className="text-xs bg-gradient-success text-white px-3 py-1 rounded-full font-medium">White Hat Security</span>
                  <span className="text-xs text-cyber-primary">Enterprise Grade</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <a href="#features" className="text-gray-300 hover:text-cyber-primary transition-colors">Features</a>
                <a href="#security" className="text-gray-300 hover:text-cyber-primary transition-colors">Security</a>
                <a href="#pricing" className="text-gray-300 hover:text-cyber-primary transition-colors">Pricing</a>
              </div>
              <Link to="/login" className="nav-link text-sm font-medium">
                Sign In
              </Link>
              <Link to="/register" className="btn-primary text-sm font-semibold">
                Start Protection
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Enhanced Hero Section */}
      <section className="relative pt-36 pb-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900/20 to-slate-900"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyber-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyber-secondary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

        <div className="relative max-w-7xl mx-auto">
          <div className="text-center">
            <div className={`transition-all duration-1000 ${isVisible ? 'slide-in' : 'opacity-0'}`}>
              {/* Status Badge */}
              <div className="inline-flex items-center space-x-2 bg-gradient-glass border border-cyber-primary/30 rounded-full px-6 py-3 mb-8">
                <div className="w-2 h-2 bg-cyber-accent-green rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-cyber-primary">Live Threat Monitoring Active</span>
                <div className="w-2 h-2 bg-cyber-accent-green rounded-full animate-pulse"></div>
              </div>

              {/* Main Headline */}
              <h1 className="text-6xl md:text-8xl font-bold mb-8 text-gradient leading-tight">
                Secure Your
                <br />
                <span className="text-cyber-primary">Digital Identity</span>
              </h1>

              {/* Subheadline */}
              <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                Enterprise-grade cybersecurity monitoring for your digital assets. Real-time threat detection,
                comprehensive breach monitoring, and advanced protection against identity theft and cyber attacks.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                <Link to="/register" className="group btn-primary text-lg px-10 py-5 flex items-center space-x-3 glow-primary transform hover:scale-105 transition-all duration-300">
                  <Shield className="h-6 w-6" />
                  <span>Start Free Protection</span>
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <button className="btn-secondary text-lg px-10 py-5 flex items-center space-x-3 hover:glow-secondary transition-all duration-300">
                  <Eye className="h-5 w-5" />
                  <span>Watch Demo</span>
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-slate-400">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-cyber-accent-green" />
                  <span>No Credit Card Required</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-cyber-primary" />
                  <span>Enterprise Security</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-cyber-accent-yellow" />
                  <span>SOC 2 Compliant</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Trusted by Security Professionals
            </h2>
            <p className="text-slate-400 text-lg max-w-2xl mx-auto">
              Real-time metrics from our global threat intelligence network
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div
                  key={index}
                  className={`cyber-card p-8 text-center transform transition-all duration-500 hover:scale-105 group ${
                    currentStat === index ? 'glow-primary scale-105' : ''
                  }`}
                >
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center mx-auto group-hover:glow-secondary transition-all duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    {currentStat === index && (
                      <div className="absolute inset-0 bg-cyber-primary/20 rounded-2xl animate-ping"></div>
                    )}
                  </div>
                  <div className="text-4xl font-bold text-white mb-3 text-gradient">{stat.number}</div>
                  <div className="text-slate-300 font-medium">{stat.label}</div>
                  <div className="mt-4 h-1 bg-gradient-secondary rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section id="features" className="py-24 px-4 sm:px-6 lg:px-8 relative">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-900/50 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-gradient-glass border border-cyber-primary/30 rounded-full px-6 py-3 mb-8">
              <Shield className="h-4 w-4 text-cyber-primary" />
              <span className="text-sm font-medium text-cyber-primary">Enterprise Security Features</span>
            </div>
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-8 text-gradient">
              Complete Digital Protection
            </h2>
            <p className="text-xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
              Advanced threat intelligence, real-time monitoring, and comprehensive security education
              to protect your digital identity from evolving cyber threats.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="cyber-card p-8 hover:glow-primary transition-all duration-500 group relative overflow-hidden">
                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-gradient-secondary opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>

                  <div className="relative z-10">
                    <div className="w-20 h-20 bg-gradient-secondary rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:glow-secondary transition-all duration-300">
                      <Icon className="h-10 w-10 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-cyber-primary transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-slate-300 leading-relaxed text-lg group-hover:text-slate-200 transition-colors duration-300">
                      {feature.description}
                    </p>

                    {/* Feature Badge */}
                    <div className="mt-6 inline-flex items-center space-x-2 text-sm text-cyber-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span>Learn More</span>
                      <ArrowRight className="h-4 w-4" />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Enhanced Security Tips Section */}
      <section id="security" className="py-24 px-4 sm:px-6 lg:px-8 relative bg-gradient-to-b from-slate-900/50 to-slate-800/30">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-gradient-glass border border-cyber-accent-green/30 rounded-full px-6 py-3 mb-8">
              <Brain className="h-4 w-4 text-cyber-accent-green" />
              <span className="text-sm font-medium text-cyber-accent-green">Security Intelligence</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Essential Security Best Practices
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Expert-recommended cybersecurity practices to strengthen your digital defense
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {securityTips.map((tip, index) => (
              <div key={index} className="cyber-card p-6 flex items-start space-x-4 hover:glow-success transition-all duration-300 group">
                <div className="w-8 h-8 bg-gradient-success rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                  <CheckCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-slate-200 text-lg leading-relaxed group-hover:text-white transition-colors duration-300">
                    {tip}
                  </p>
                  <div className="mt-3 h-0.5 bg-gradient-success rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section id="pricing" className="py-24 px-4 sm:px-6 lg:px-8 relative">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-transparent to-transparent"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyber-primary/5 rounded-full blur-3xl"></div>

        <div className="relative max-w-5xl mx-auto text-center">
          <div className="cyber-card p-16 glow-primary relative overflow-hidden">
            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-cyber-primary/20 to-transparent"></div>
            </div>

            <div className="relative z-10">
              {/* Status Indicator */}
              <div className="inline-flex items-center space-x-2 bg-gradient-success rounded-full px-6 py-3 mb-8">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-white">Free Tier Available</span>
              </div>

              <h2 className="text-5xl md:text-6xl font-bold text-white mb-8 text-gradient">
                Secure Your Digital Future
              </h2>
              <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                Join over 100,000 security-conscious users who trust Identity Guardian to protect their digital identities.
                Start with our free tier and upgrade as your security needs grow.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                <Link to="/register" className="group btn-primary text-xl px-12 py-6 flex items-center justify-center space-x-3 glow-primary transform hover:scale-105 transition-all duration-300">
                  <Shield className="h-6 w-6" />
                  <span>Start Free Protection</span>
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link to="/login" className="btn-secondary text-xl px-12 py-6 flex items-center justify-center space-x-3 hover:glow-secondary transition-all duration-300">
                  <Users className="h-6 w-6" />
                  <span>Access Dashboard</span>
                </Link>
              </div>

              {/* Security Badges */}
              <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-slate-400">
                <div className="flex items-center space-x-2">
                  <Lock className="h-5 w-5 text-cyber-primary" />
                  <span>256-bit Encryption</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-cyber-accent-yellow" />
                  <span>SOC 2 Type II</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="h-5 w-5 text-cyber-accent-green" />
                  <span>Global Coverage</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Footer */}
      <footer className="py-16 px-4 sm:px-6 lg:px-8 border-t border-slate-800/50 bg-gradient-to-b from-transparent to-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {/* Brand */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-secondary rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <span className="text-xl font-bold text-white">Identity Guardian</span>
                  <div className="text-xs text-cyber-primary">Enterprise Security</div>
                </div>
              </div>
              <p className="text-slate-400 text-sm leading-relaxed">
                Advanced cybersecurity monitoring and protection for your digital identity.
                Trusted by security professionals worldwide.
              </p>
            </div>

            {/* Security Info */}
            <div className="space-y-4">
              <h3 className="text-white font-semibold">Security Standards</h3>
              <div className="space-y-2 text-sm text-slate-400">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-cyber-accent-green" />
                  <span>ISO 27001 Certified</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-cyber-accent-green" />
                  <span>GDPR Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-cyber-accent-green" />
                  <span>24/7 Security Monitoring</span>
                </div>
              </div>
            </div>

            {/* Contact */}
            <div className="space-y-4">
              <h3 className="text-white font-semibold">Get Started</h3>
              <div className="space-y-3">
                <Link to="/register" className="block text-cyber-primary hover:text-cyber-primary-light transition-colors text-sm">
                  Create Free Account
                </Link>
                <Link to="/login" className="block text-slate-400 hover:text-white transition-colors text-sm">
                  Sign In
                </Link>
                <a href="#features" className="block text-slate-400 hover:text-white transition-colors text-sm">
                  View Features
                </a>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="pt-8 border-t border-slate-800/50 flex flex-col md:flex-row justify-between items-center">
            <div className="text-slate-400 text-sm mb-4 md:mb-0">
              © 2024 Identity Guardian. All rights reserved. White Hat Security Solutions.
            </div>
            <div className="flex items-center space-x-4 text-xs text-slate-500">
              <span>Powered by Advanced AI</span>
              <span>•</span>
              <span>Real-time Threat Intelligence</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;


# Identity Guardian - Python Dependencies
# Backend requirements for Flask application

# Core Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
Flask-Limiter==3.5.0
Werkzeug==2.3.7

# Database
psycopg2-binary==2.9.7

# Configuration & Environment
python-dotenv==1.0.0
pydantic==2.4.2
pydantic-settings==2.0.3

# Security & Authentication
bcrypt==4.0.1
PyJWT==2.8.0
pwnedpasswords==2.0.0

# HTTP & API
requests==2.31.0
urllib3==2.0.4
certifi==2023.7.22
charset-normalizer==3.2.0
idna==3.4

# Background Tasks
celery==5.3.4
redis==5.0.1

# Communication Services
sendgrid==6.10.0
twilio==8.9.1
phonenumbers==8.13.19

# Core Python Libraries
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
six==1.16.0

# API Documentation
flasgger==0.9.7.1

# Development dependencies
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
black==23.7.0
flake8==6.0.0
mypy==1.5.1
isort==5.12.0

# Production dependencies
gunicorn==21.2.0
supervisor==4.2.5


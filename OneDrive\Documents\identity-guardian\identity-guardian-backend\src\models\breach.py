from datetime import datetime
from enum import Enum
from src import db

class BreachSeverity(Enum):
    """Enumeration for breach severity levels."""
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'

class BreachStatus(Enum):
    """Enumeration for breach status."""
    DETECTED = 'detected'
    CONFIRMED = 'confirmed'
    RESOLVED = 'resolved'
    FALSE_POSITIVE = 'false_positive'

class Breach(db.Model):
    """Breach model for storing detected security breaches."""
    
    __tablename__ = 'breaches'
    
    id = db.Column(db.Integer, primary_key=True)
    identity_id = db.Column(db.Integer, db.ForeignKey('identities.id'), nullable=False, index=True)
    
    # Breach information
    source = db.Column(db.String(255), nullable=False)  # e.g., "HaveIBeenPwned", "BreachDirectory"
    breach_name = db.Column(db.String(255), nullable=False)  # e.g., "Adobe", "LinkedIn"
    breach_date = db.Column(db.Date)  # When the actual breach occurred
    detected_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)  # When we detected it
    
    # Breach details
    severity = db.Column(db.Enum(BreachSeverity), default=BreachSeverity.MEDIUM, nullable=False)
    status = db.Column(db.Enum(BreachStatus), default=BreachStatus.DETECTED, nullable=False)
    description = db.Column(db.Text)
    affected_data = db.Column(db.JSON)  # List of data types affected (emails, passwords, etc.)
    
    # Additional metadata
    external_id = db.Column(db.String(255))  # ID from external breach database
    verification_status = db.Column(db.String(50))  # verified, unverified, etc.
    breach_url = db.Column(db.String(500))  # URL to more information
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, identity_id, source, breach_name, breach_date=None, severity=BreachSeverity.MEDIUM):
        self.identity_id = identity_id
        self.source = source
        self.breach_name = breach_name
        self.breach_date = breach_date
        self.severity = severity
    
    def update_status(self, status, description=None):
        """Update breach status and description."""
        self.status = status
        if description:
            self.description = description
        self.updated_at = datetime.utcnow()
    
    def add_affected_data(self, data_types):
        """Add or update affected data types."""
        if self.affected_data is None:
            self.affected_data = []
        
        if isinstance(data_types, list):
            self.affected_data.extend(data_types)
        else:
            self.affected_data.append(data_types)
        
        # Remove duplicates
        self.affected_data = list(set(self.affected_data))
        self.updated_at = datetime.utcnow()
    
    @property
    def is_recent(self):
        """Check if breach was detected recently (within 30 days)."""
        if not self.detected_at:
            return False
        
        days_since_detection = (datetime.utcnow() - self.detected_at).days
        return days_since_detection <= 30
    
    @property
    def severity_score(self):
        """Get numeric severity score (1-4)."""
        severity_scores = {
            BreachSeverity.LOW: 1,
            BreachSeverity.MEDIUM: 2,
            BreachSeverity.HIGH: 3,
            BreachSeverity.CRITICAL: 4
        }
        return severity_scores.get(self.severity, 2)
    
    def to_dict(self):
        """Convert breach to dictionary."""
        return {
            'id': self.id,
            'identity_id': self.identity_id,
            'source': self.source,
            'breach_name': self.breach_name,
            'breach_date': self.breach_date.isoformat() if self.breach_date else None,
            'detected_at': self.detected_at.isoformat() if self.detected_at else None,
            'severity': self.severity.value,
            'severity_score': self.severity_score,
            'status': self.status.value,
            'description': self.description,
            'affected_data': self.affected_data or [],
            'external_id': self.external_id,
            'verification_status': self.verification_status,
            'breach_url': self.breach_url,
            'is_recent': self.is_recent,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Breach {self.breach_name} - {self.severity.value}>'


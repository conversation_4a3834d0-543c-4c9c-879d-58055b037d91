import React, { useState } from 'react';
import Layout from '../components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription } from '../components/ui/alert';
import { 
  Eye, 
  Mail, 
  Smartphone, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Plus,
  Search,
  Globe,
  Clock,
  TrendingUp,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

const IdentitiesPage = () => {
  const [identities, setIdentities] = useState([
    {
      id: 1,
      type: 'email',
      value: '<EMAIL>',
      status: 'monitored',
      riskLevel: 'medium',
      breachCount: 2,
      lastBreach: '2023-08-15',
      lastChecked: '2 hours ago',
      breaches: [
        { name: 'LinkedIn', date: '2021-06-01', severity: 'medium' },
        { name: 'Adobe', date: '2013-10-01', severity: 'high' }
      ]
    },
    {
      id: 2,
      type: 'email',
      value: '<EMAIL>',
      status: 'monitored',
      riskLevel: 'low',
      breachCount: 0,
      lastBreach: null,
      lastChecked: '1 hour ago',
      breaches: []
    },
    {
      id: 3,
      type: 'phone',
      value: '+****************',
      status: 'monitored',
      riskLevel: 'high',
      breachCount: 1,
      lastBreach: '2024-01-10',
      lastChecked: '30 minutes ago',
      breaches: [
        { name: 'T-Mobile', date: '2024-01-10', severity: 'critical' }
      ]
    },
    {
      id: 4,
      type: 'phone',
      value: '+****************',
      status: 'monitored',
      riskLevel: 'low',
      breachCount: 0,
      lastBreach: null,
      lastChecked: '45 minutes ago',
      breaches: []
    }
  ]);

  const [newIdentity, setNewIdentity] = useState({ type: 'email', value: '' });
  const [searchTerm, setSearchTerm] = useState('');

  const getRiskColor = (level) => {
    switch (level) {
      case 'critical': return 'text-red-400 bg-red-500/20 border-red-500/50';
      case 'high': return 'text-orange-400 bg-orange-500/20 border-orange-500/50';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/50';
      case 'low': return 'text-green-400 bg-green-500/20 border-green-500/50';
      default: return 'text-slate-400 bg-slate-500/20 border-slate-500/50';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'monitored': return 'text-green-400 bg-green-500/20 border-green-500/50';
      case 'paused': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/50';
      case 'error': return 'text-red-400 bg-red-500/20 border-red-500/50';
      default: return 'text-slate-400 bg-slate-500/20 border-slate-500/50';
    }
  };

  const addIdentity = () => {
    if (!newIdentity.value.trim()) {
      toast.error('Please enter a valid identity');
      return;
    }

    const identity = {
      id: Date.now(),
      type: newIdentity.type,
      value: newIdentity.value.trim(),
      status: 'monitored',
      riskLevel: 'low',
      breachCount: 0,
      lastBreach: null,
      lastChecked: 'Just now',
      breaches: []
    };

    setIdentities([...identities, identity]);
    setNewIdentity({ type: 'email', value: '' });
    toast.success('Identity added and monitoring started!');
  };

  const filteredIdentities = identities.filter(identity =>
    identity.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stats = {
    total: identities.length,
    emails: identities.filter(i => i.type === 'email').length,
    phones: identities.filter(i => i.type === 'phone').length,
    breaches: identities.reduce((sum, i) => sum + i.breachCount, 0),
    highRisk: identities.filter(i => ['high', 'critical'].includes(i.riskLevel)).length
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Identity Monitoring</h1>
            <p className="text-slate-400">
              Monitor your phone numbers and email addresses for breaches and unauthorized use
            </p>
          </div>
          <Button className="bg-purple-600 hover:bg-purple-700 mt-4 md:mt-0">
            <Plus className="w-4 h-4 mr-2" />
            Add Identity
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Identities</CardTitle>
              <Eye className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.total}</div>
              <p className="text-xs text-slate-400">Being monitored</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Email Addresses</CardTitle>
              <Mail className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.emails}</div>
              <p className="text-xs text-slate-400">Protected</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Phone Numbers</CardTitle>
              <Smartphone className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.phones}</div>
              <p className="text-xs text-slate-400">Secured</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Breaches Found</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.breaches}</div>
              <p className="text-xs text-slate-400">Total detected</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">High Risk</CardTitle>
              <Shield className="h-4 w-4 text-orange-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.highRisk}</div>
              <p className="text-xs text-slate-400">Need attention</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="monitor" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
            <TabsTrigger value="monitor" className="data-[state=active]:bg-purple-600">
              Monitor Identities
            </TabsTrigger>
            <TabsTrigger value="add" className="data-[state=active]:bg-purple-600">
              Add New Identity
            </TabsTrigger>
            <TabsTrigger value="reports" className="data-[state=active]:bg-purple-600">
              Reports & Analytics
            </TabsTrigger>
          </TabsList>

          {/* Monitor Tab */}
          <TabsContent value="monitor" className="space-y-6">
            {/* Search */}
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search identities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                />
              </div>
              <Button variant="outline" className="border-slate-600 text-slate-300">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>

            {/* Identity List */}
            <div className="space-y-4">
              {filteredIdentities.map((identity) => (
                <Card key={identity.id} className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
                          {identity.type === 'email' ? (
                            <Mail className="h-6 w-6 text-purple-400" />
                          ) : (
                            <Smartphone className="h-6 w-6 text-purple-400" />
                          )}
                        </div>
                        <div>
                          <h3 className="text-white font-medium">{identity.value}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge className={getStatusColor(identity.status)}>
                              {identity.status}
                            </Badge>
                            <Badge className={getRiskColor(identity.riskLevel)}>
                              {identity.riskLevel} risk
                            </Badge>
                          </div>
                          <p className="text-slate-400 text-sm mt-1">
                            Last checked: {identity.lastChecked}
                          </p>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center space-x-2 mb-2">
                          {identity.breachCount > 0 ? (
                            <AlertTriangle className="h-5 w-5 text-red-400" />
                          ) : (
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          )}
                          <span className="text-white font-medium">
                            {identity.breachCount} breach{identity.breachCount !== 1 ? 'es' : ''}
                          </span>
                        </div>
                        {identity.lastBreach && (
                          <p className="text-slate-400 text-sm">
                            Last breach: {identity.lastBreach}
                          </p>
                        )}
                      </div>
                    </div>

                    {identity.breaches.length > 0 && (
                      <div className="mt-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                        <h4 className="text-white font-medium mb-3">Detected Breaches</h4>
                        <div className="space-y-2">
                          {identity.breaches.map((breach, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                                  <AlertTriangle className="h-4 w-4 text-red-400" />
                                </div>
                                <div>
                                  <p className="text-white font-medium">{breach.name}</p>
                                  <p className="text-slate-400 text-sm">{breach.date}</p>
                                </div>
                              </div>
                              <Badge className={getRiskColor(breach.severity)}>
                                {breach.severity}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Add Identity Tab */}
          <TabsContent value="add" className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Plus className="w-5 h-5 mr-2 text-purple-400" />
                  Add New Identity
                </CardTitle>
                <CardDescription>
                  Start monitoring a new email address or phone number for breaches
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-slate-300">Identity Type</Label>
                  <div className="flex space-x-4">
                    <Button
                      variant={newIdentity.type === 'email' ? 'default' : 'outline'}
                      onClick={() => setNewIdentity({ ...newIdentity, type: 'email' })}
                      className={newIdentity.type === 'email' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Email Address
                    </Button>
                    <Button
                      variant={newIdentity.type === 'phone' ? 'default' : 'outline'}
                      onClick={() => setNewIdentity({ ...newIdentity, type: 'phone' })}
                      className={newIdentity.type === 'phone' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}
                    >
                      <Smartphone className="w-4 h-4 mr-2" />
                      Phone Number
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-300">
                    {newIdentity.type === 'email' ? 'Email Address' : 'Phone Number'}
                  </Label>
                  <Input
                    placeholder={newIdentity.type === 'email' ? 'Enter email address' : 'Enter phone number'}
                    value={newIdentity.value}
                    onChange={(e) => setNewIdentity({ ...newIdentity, value: e.target.value })}
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>

                <Button onClick={addIdentity} className="w-full bg-purple-600 hover:bg-purple-700">
                  <Eye className="w-4 h-4 mr-2" />
                  Start Monitoring
                </Button>

                <Alert className="border-blue-500/50 bg-blue-500/10">
                  <Shield className="h-4 w-4" />
                  <AlertDescription className="text-blue-300">
                    <strong>Privacy Notice:</strong> We only store hashed versions of your identities and never share your personal information with third parties.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Information Cards */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">What We Monitor</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Globe className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Global Breach Databases</p>
                      <p className="text-slate-400 text-sm">Monitor major data breaches worldwide</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Eye className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Dark Web Monitoring</p>
                      <p className="text-slate-400 text-sm">Scan underground markets and forums</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Clock className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Real-time Alerts</p>
                      <p className="text-slate-400 text-sm">Instant notifications when breaches are detected</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Security Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Encrypted Storage</p>
                      <p className="text-slate-400 text-sm">All data encrypted with AES-256</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Privacy First</p>
                      <p className="text-slate-400 text-sm">No data sharing with third parties</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Risk Assessment</p>
                      <p className="text-slate-400 text-sm">Advanced algorithms for threat analysis</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Monitoring Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Total Identities</span>
                    <span className="text-white font-medium">{stats.total}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Active Monitoring</span>
                    <span className="text-green-400 font-medium">{stats.total}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Breaches Detected</span>
                    <span className="text-red-400 font-medium">{stats.breaches}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">High Risk Items</span>
                    <span className="text-orange-400 font-medium">{stats.highRisk}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Recent Activity</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">All identities checked - 2 hours ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">New breach detected - 1 day ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">Identity added - 3 days ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">Weekly scan completed - 1 week ago</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default IdentitiesPage;


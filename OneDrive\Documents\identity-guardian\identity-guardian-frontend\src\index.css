@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Cybersecurity Design System */
:root {
  /* Primary Cybersecurity Color Palette */
  --cyber-primary: #00d4ff;
  --cyber-primary-dark: #0099cc;
  --cyber-primary-light: #33ddff;
  --cyber-secondary: #ff6b35;
  --cyber-secondary-dark: #cc5529;
  --cyber-secondary-light: #ff8559;

  /* Accent Colors */
  --cyber-accent-green: #00ff88;
  --cyber-accent-purple: #8b5cf6;
  --cyber-accent-red: #ef4444;
  --cyber-accent-yellow: #fbbf24;

  /* Background Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    #0f172a 0%,
    #1e293b 25%,
    #334155 50%,
    #475569 75%,
    #64748b 100%
  );
  --gradient-secondary: linear-gradient(135deg, #00d4ff 0%, #8b5cf6 100%);
  --gradient-accent: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);

  /* Dark Theme Backgrounds */
  --gradient-dark: linear-gradient(
    135deg,
    #020617 0%,
    #0f172a 25%,
    #1e293b 50%,
    #334155 100%
  );
  --gradient-card: linear-gradient(
    145deg,
    rgba(15, 23, 42, 0.8) 0%,
    rgba(30, 41, 59, 0.6) 100%
  );
  --gradient-glass: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );

  /* Enhanced Shadows & Effects */
  --shadow-cyber: 0 0 30px rgba(0, 212, 255, 0.3);
  --shadow-card: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-elevated: 0 35px 60px -12px rgba(0, 0, 0, 0.6);
  --shadow-glow-primary: 0 0 40px rgba(0, 212, 255, 0.4);
  --shadow-glow-secondary: 0 0 40px rgba(255, 107, 53, 0.4);
  --shadow-glow-success: 0 0 40px rgba(0, 255, 136, 0.4);
  --shadow-glow-danger: 0 0 40px rgba(239, 68, 68, 0.4);

  /* Border Effects */
  --border-cyber: 1px solid rgba(0, 212, 255, 0.3);
  --border-glow: 1px solid rgba(0, 212, 255, 0.5);
  --border-glass: 1px solid rgba(255, 255, 255, 0.1);
  --border-subtle: 1px solid rgba(148, 163, 184, 0.2);
}

/* Enhanced Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;
  background: var(--gradient-dark);
  color: #f8fafc;
  line-height: 1.6;
  overflow-x: hidden;
  font-weight: 400;
  letter-spacing: -0.01em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Gradient Backgrounds */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.bg-gradient-danger {
  background: var(--gradient-danger);
}

.bg-gradient-warning {
  background: var(--gradient-warning);
}

.bg-gradient-success {
  background: var(--gradient-success);
}

.bg-gradient-dark {
  background: var(--gradient-dark);
}

.bg-gradient-card {
  background: var(--gradient-card);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: var(--border-glow);
}

.bg-gradient-glass {
  background: var(--gradient-glass);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: var(--border-glass);
}

/* Animated Background */
.animated-bg {
  background: linear-gradient(-45deg, #0c0c0c, #1a1a2e, #16213e, #0f3460);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Glowing Effects */
.glow-effect {
  box-shadow: var(--shadow-glow);
  transition: all 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 40px rgba(102, 126, 234, 0.5);
  transform: translateY(-2px);
}

/* Enhanced Card Styles */
.glass-card {
  background: var(--gradient-glass);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: var(--border-glass);
  border-radius: 16px;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
}

.glass-card:hover {
  box-shadow: var(--shadow-elevated);
  border: var(--border-cyber);
  transform: translateY(-4px);
}

.cyber-card {
  background: var(--gradient-card);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: var(--border-cyber);
  border-radius: 12px;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cyber-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.cyber-card:hover::before {
  left: 100%;
}

.cyber-card:hover {
  box-shadow: var(--shadow-glow-primary);
  border: var(--border-glow);
  transform: translateY(-2px);
}

/* Button Styles */
.btn-primary {
  background: var(--gradient-primary);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.btn-secondary {
  background: var(--gradient-glass);
  border: var(--border-glass);
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(102, 126, 234, 0.5);
  transform: translateY(-2px);
}

/* Enhanced Input Styles */
.input-field {
  background: var(--gradient-glass);
  border: var(--border-glass);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.input-field:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.8);
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Navigation Styles */
.nav-link {
  position: relative;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  color: white;
  background: var(--gradient-primary);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Progress Bar */
.progress-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  height: 8px;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-accent);
  border-radius: 10px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Badge Styles */
.badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.badge-danger {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  color: white;
}

/* Loading Animation */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Floating Animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse Animation */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Slide In Animation */
.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Scale In Animation */
.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

/* Text Gradient */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-secondary);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .glass-card {
    margin: 8px;
    border-radius: 12px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 10px 20px;
    font-size: 14px;
  }

  .input-field {
    padding: 10px 14px;
  }
}

/* Dark mode enhancements */
.dark {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

/* Custom utility classes */
.backdrop-blur-glass {
  backdrop-filter: blur(20px);
}

.border-gradient {
  border-image: var(--gradient-primary) 1;
}

.shadow-glow-purple {
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

.shadow-glow-blue {
  box-shadow: 0 0 30px rgba(79, 172, 254, 0.3);
}

/* Typography */
.heading-gradient {
  background: linear-gradient(135deg, #ffffff 0%, #667eea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

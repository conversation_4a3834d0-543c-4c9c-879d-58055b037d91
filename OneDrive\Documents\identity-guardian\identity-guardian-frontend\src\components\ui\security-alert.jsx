import React from 'react';
import { cn } from '@/lib/utils';
import { X, AlertTriangle, Shield, CheckCircle, Info, Zap } from 'lucide-react';

const SecurityAlert = React.forwardRef(({ 
  className, 
  variant = 'info',
  title,
  description,
  dismissible = false,
  onDismiss,
  action,
  icon: CustomIcon,
  ...props 
}, ref) => {
  const variants = {
    info: {
      container: 'bg-cyber-primary/10 border border-cyber-primary/30 text-cyber-primary',
      icon: Info,
      iconBg: 'bg-cyber-primary/20',
      iconColor: 'text-cyber-primary'
    },
    success: {
      container: 'bg-cyber-accent-green/10 border border-cyber-accent-green/30 text-cyber-accent-green',
      icon: CheckCircle,
      iconBg: 'bg-cyber-accent-green/20',
      iconColor: 'text-cyber-accent-green'
    },
    warning: {
      container: 'bg-cyber-accent-yellow/10 border border-cyber-accent-yellow/30 text-cyber-accent-yellow',
      icon: Alert<PERSON>riangle,
      iconBg: 'bg-cyber-accent-yellow/20',
      iconColor: 'text-cyber-accent-yellow'
    },
    danger: {
      container: 'bg-cyber-accent-red/10 border border-cyber-accent-red/30 text-cyber-accent-red',
      icon: AlertTriangle,
      iconBg: 'bg-cyber-accent-red/20',
      iconColor: 'text-cyber-accent-red'
    },
    security: {
      container: 'bg-gradient-secondary/10 border border-cyber-primary/30 text-white',
      icon: Shield,
      iconBg: 'bg-gradient-secondary',
      iconColor: 'text-white'
    },
    critical: {
      container: 'bg-gradient-danger/20 border border-cyber-accent-red/50 text-white animate-pulse',
      icon: Zap,
      iconBg: 'bg-gradient-danger',
      iconColor: 'text-white'
    }
  };

  const currentVariant = variants[variant] || variants.info;
  const IconComponent = CustomIcon || currentVariant.icon;

  return (
    <div
      className={cn(
        'rounded-2xl p-6 transition-all duration-300 hover:scale-[1.02] relative overflow-hidden',
        currentVariant.container,
        className
      )}
      ref={ref}
      {...props}
    >
      {/* Background Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
      
      <div className="relative z-10 flex items-start space-x-4">
        {/* Icon */}
        <div className={cn(
          'w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300',
          currentVariant.iconBg
        )}>
          <IconComponent className={cn('h-6 w-6', currentVariant.iconColor)} />
        </div>
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className="font-semibold text-lg mb-2 text-white">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm leading-relaxed opacity-90">
              {description}
            </p>
          )}
          
          {/* Action */}
          {action && (
            <div className="mt-4">
              {action}
            </div>
          )}
        </div>
        
        {/* Dismiss Button */}
        {dismissible && (
          <button
            onClick={onDismiss}
            className="w-8 h-8 rounded-lg flex items-center justify-center hover:bg-white/10 transition-colors duration-200 flex-shrink-0"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
});

SecurityAlert.displayName = 'SecurityAlert';

export { SecurityAlert };

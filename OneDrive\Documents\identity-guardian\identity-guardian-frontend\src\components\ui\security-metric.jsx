import React from 'react';
import { cn } from '@/lib/utils';

const SecurityMetric = React.forwardRef(({ 
  className, 
  variant = 'default',
  title,
  value,
  max,
  description,
  icon: Icon,
  status = 'normal',
  trend,
  ...props 
}, ref) => {
  const variants = {
    default: 'cyber-card p-8 hover:glow-primary transition-all duration-500 group relative overflow-hidden',
    compact: 'cyber-card p-6 hover:glow-secondary transition-all duration-300 group',
    minimal: 'bg-gradient-glass border border-slate-700/50 rounded-2xl p-6 hover:border-cyber-primary/30 transition-all duration-300',
  };

  const statusColors = {
    excellent: {
      bg: 'bg-gradient-success',
      text: 'text-cyber-accent-green',
      glow: 'glow-success',
      badge: 'bg-cyber-accent-green/20 text-cyber-accent-green border-cyber-accent-green/30'
    },
    good: {
      bg: 'bg-gradient-secondary',
      text: 'text-cyber-primary',
      glow: 'glow-primary',
      badge: 'bg-cyber-primary/20 text-cyber-primary border-cyber-primary/30'
    },
    warning: {
      bg: 'bg-gradient-warning',
      text: 'text-cyber-accent-yellow',
      glow: 'glow-warning',
      badge: 'bg-cyber-accent-yellow/20 text-cyber-accent-yellow border-cyber-accent-yellow/30'
    },
    critical: {
      bg: 'bg-gradient-danger',
      text: 'text-cyber-accent-red',
      glow: 'glow-danger',
      badge: 'bg-cyber-accent-red/20 text-cyber-accent-red border-cyber-accent-red/30'
    },
    normal: {
      bg: 'bg-gradient-secondary',
      text: 'text-slate-300',
      glow: 'glow-primary',
      badge: 'bg-slate-700/50 text-slate-300 border-slate-600/30'
    }
  };

  const currentStatus = statusColors[status] || statusColors.normal;
  const percentage = max ? Math.round((value / max) * 100) : null;

  return (
    <div
      className={cn(variants[variant], className)}
      ref={ref}
      {...props}
    >
      {/* Background Animation */}
      <div className="absolute inset-0 bg-gradient-secondary opacity-0 group-hover:opacity-5 transition-opacity duration-500"></div>
      
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          {Icon && (
            <div className={cn(
              'w-16 h-16 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300',
              currentStatus.bg,
              `group-hover:${currentStatus.glow}`
            )}>
              <Icon className="h-8 w-8 text-white" />
            </div>
          )}
          
          {status !== 'normal' && (
            <div className={cn(
              'px-3 py-1 rounded-full text-xs font-semibold border transition-all duration-300',
              currentStatus.badge
            )}>
              {status.toUpperCase()}
            </div>
          )}
        </div>
        
        {/* Metrics */}
        <div className="mb-6">
          <div className="flex items-baseline space-x-2 mb-2">
            <span className="text-4xl font-bold text-white text-gradient">
              {value}
            </span>
            {max && (
              <span className="text-slate-400 text-lg">/{max}</span>
            )}
            {trend && (
              <span className={cn(
                'text-sm font-medium',
                trend > 0 ? 'text-cyber-accent-green' : trend < 0 ? 'text-cyber-accent-red' : 'text-slate-400'
              )}>
                {trend > 0 ? '+' : ''}{trend}%
              </span>
            )}
          </div>
          <h3 className={cn(
            'font-semibold text-lg group-hover:text-white transition-colors duration-300',
            currentStatus.text
          )}>
            {title}
          </h3>
        </div>
        
        {/* Progress Bar */}
        {max && percentage !== null && (
          <div className="space-y-2 mb-6">
            <div className="flex justify-between text-xs text-slate-400">
              <span>Progress</span>
              <span>{percentage}%</span>
            </div>
            <div className="w-full bg-slate-700/50 rounded-full h-3 overflow-hidden">
              <div 
                className={cn(
                  'h-3 rounded-full transition-all duration-1000 relative overflow-hidden',
                  currentStatus.bg
                )}
                style={{ width: `${percentage}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
              </div>
            </div>
          </div>
        )}
        
        {/* Description */}
        {description && (
          <p className="text-slate-400 text-sm leading-relaxed group-hover:text-slate-300 transition-colors duration-300">
            {description}
          </p>
        )}
      </div>
    </div>
  );
});

SecurityMetric.displayName = 'SecurityMetric';

export { SecurityMetric };

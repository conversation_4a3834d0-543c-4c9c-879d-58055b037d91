import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  Shield, 
  Eye, 
  EyeOff, 
  Lock, 
  Mail, 
  ArrowRight,
  CheckCircle,
  Zap,
  Globe
} from 'lucide-react';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Simulate login
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (email === '<EMAIL>' && password === 'SecureDemo123!') {
        login({ email, name: 'Demo User' });
        navigate('/dashboard');
      } else {
        setError('Invalid credentials. Use <EMAIL> / SecureDemo123!');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
      icon: Shield,
      title: 'Advanced Protection',
      description: '256-bit encryption and multi-layer security'
    },
    {
      icon: Zap,
      title: 'Real-time Monitoring',
      description: 'Instant alerts for any suspicious activity'
    },
    {
      icon: Globe,
      title: 'Global Coverage',
      description: 'Worldwide breach database monitoring'
    }
  ];

  return (
    <div className="min-h-screen animated-bg flex">
      {/* Left Side - Login Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="text-center mb-8 slide-in">
            <Link to="/" className="inline-flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center glow-effect">
                <Shield className="h-7 w-7 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">Identity Guardian</span>
            </Link>
            <h1 className="text-3xl font-bold text-white mb-2">Secure your digital identity</h1>
            <p className="text-gray-300">Welcome Back</p>
            <p className="text-sm text-gray-400 mt-2">Sign in to your Identity Guardian account</p>
          </div>

          {/* Demo Credentials Info */}
          <div className="glass-card p-4 mb-6 border border-blue-500/30">
            <h3 className="text-white font-semibold mb-2 flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span>Demo Credentials:</span>
            </h3>
            <div className="space-y-1 text-sm">
              <p className="text-gray-300">
                <span className="text-blue-400">Email:</span> <EMAIL>
              </p>
              <p className="text-gray-300">
                <span className="text-blue-400">Password:</span> SecureDemo123!
              </p>
            </div>
            <p className="text-xs text-gray-400 mt-2">
              Protected by 256-bit encryption and advanced security measures
            </p>
          </div>

          {/* Login Form */}
          <div className="glass-card p-8 scale-in">
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/30 text-red-300 text-sm">
                  {error}
                </div>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Email
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input-field w-full pl-10"
                    placeholder="Enter your email"
                    required
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input-field w-full pl-10 pr-10"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input type="checkbox" className="rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500" />
                  <span className="ml-2 text-sm text-gray-300">Remember me</span>
                </label>
                <Link to="/forgot-password" className="text-sm text-blue-400 hover:text-blue-300 transition-colors">
                  Forgot password?
                </Link>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary w-full flex items-center justify-center space-x-2 py-3 text-lg glow-effect"
              >
                {isLoading ? (
                  <div className="loading-spinner w-5 h-5"></div>
                ) : (
                  <>
                    <span>Sign In</span>
                    <ArrowRight className="h-5 w-5" />
                  </>
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-gray-400">
                Don't have an account?{' '}
                <Link to="/register" className="text-blue-400 hover:text-blue-300 transition-colors font-medium">
                  Create one here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Features */}
      <div className="hidden lg:flex flex-1 items-center justify-center p-8">
        <div className="max-w-lg">
          <div className="text-center mb-12 fade-in">
            <h2 className="text-4xl font-bold text-white mb-4 heading-gradient">
              Comprehensive Cybersecurity Protection
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed">
              Advanced monitoring and protection for your digital identity, 
              passwords, and personal information.
            </p>
          </div>

          <div className="space-y-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div 
                  key={index} 
                  className="glass-card p-6 hover-lift fade-in"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                      <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 grid grid-cols-3 gap-6 text-center">
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-white mb-1">99.9%</div>
              <div className="text-sm text-gray-400">Uptime</div>
            </div>
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-white mb-1">50M+</div>
              <div className="text-sm text-gray-400">Protected</div>
            </div>
            <div className="glass-card p-4">
              <div className="text-2xl font-bold text-white mb-1">24/7</div>
              <div className="text-sm text-gray-400">Monitoring</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;


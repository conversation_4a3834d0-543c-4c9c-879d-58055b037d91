import {
    <PERSON>R<PERSON>,
    CheckCircle,
    Clock,
    Eye,
    EyeOff,
    Globe,
    Lock,
    Mail,
    Shield,
    Users,
    Zap
} from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Simulate login
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (email === '<EMAIL>' && password === 'SecureDemo123!') {
        login({ email, name: 'Demo User' });
        navigate('/dashboard');
      } else {
        setError('Invalid credentials. Use <EMAIL> / SecureDemo123!');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
      icon: Shield,
      title: 'Advanced Protection',
      description: '256-bit encryption and multi-layer security'
    },
    {
      icon: Zap,
      title: 'Real-time Monitoring',
      description: 'Instant alerts for any suspicious activity'
    },
    {
      icon: Globe,
      title: 'Global Coverage',
      description: 'Worldwide breach database monitoring'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-dark flex">
      {/* Left Side - Enhanced Login Form */}
      <div className="flex-1 flex items-center justify-center p-8 relative">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900/20 to-slate-900"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyber-primary/10 rounded-full blur-3xl animate-pulse"></div>

        <div className="relative w-full max-w-lg z-10">
          {/* Enhanced Logo */}
          <div className="text-center mb-12 slide-in">
            <Link to="/" className="inline-flex items-center space-x-4 mb-8 group">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-9 w-9 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-cyber-accent-green rounded-full animate-pulse"></div>
              </div>
              <div className="text-left">
                <span className="text-3xl font-bold text-white group-hover:text-cyber-primary transition-colors duration-300">
                  Identity Guardian
                </span>
                <div className="text-sm text-cyber-primary font-medium">Enterprise Security</div>
              </div>
            </Link>

            <h1 className="text-4xl font-bold text-white mb-4 text-gradient">
              Secure Access Portal
            </h1>
            <p className="text-xl text-slate-300 mb-2">Welcome Back</p>
            <p className="text-slate-400">Sign in to your cybersecurity command center</p>
          </div>

          {/* Enhanced Demo Credentials Info */}
          <div className="cyber-card p-6 mb-8 border border-cyber-accent-green/30 hover:glow-success transition-all duration-300">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-success rounded-xl flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-white font-bold">Demo Access Credentials</h3>
                <p className="text-cyber-accent-green text-sm">Instant secure access</p>
              </div>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between p-3 bg-gradient-glass rounded-xl border border-slate-700/50">
                <span className="text-slate-300">Email:</span>
                <span className="text-cyber-primary font-mono"><EMAIL></span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-glass rounded-xl border border-slate-700/50">
                <span className="text-slate-300">Password:</span>
                <span className="text-cyber-primary font-mono">SecureDemo123!</span>
              </div>
            </div>
            <div className="mt-4 flex items-center space-x-2 text-xs text-slate-400">
              <Lock className="h-3 w-3" />
              <span>256-bit encryption • SOC 2 compliant • Zero-trust architecture</span>
            </div>
          </div>

          {/* Enhanced Login Form */}
          <div className="cyber-card p-10 scale-in hover:glow-primary transition-all duration-500">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-white mb-2">Secure Authentication</h2>
              <p className="text-slate-400">Enter your credentials to access the security dashboard</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {error && (
                <div className="p-4 rounded-2xl bg-gradient-danger/10 border border-cyber-accent-red/30 text-cyber-accent-red text-sm flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-danger rounded-xl flex items-center justify-center flex-shrink-0">
                    <Shield className="h-4 w-4 text-white" />
                  </div>
                  <span>{error}</span>
                </div>
              )}

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-semibold text-slate-300 mb-3">
                  Email Address
                </label>
                <div className="relative group">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-cyber-primary/20 rounded-lg flex items-center justify-center group-focus-within:bg-cyber-primary/30 transition-colors duration-300">
                    <Mail className="h-3 w-3 text-cyber-primary" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-14 pr-4 py-4 bg-gradient-glass border border-slate-700/50 rounded-2xl text-white placeholder-slate-400 focus:border-cyber-primary/50 focus:ring-2 focus:ring-cyber-primary/20 transition-all duration-300"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-semibold text-slate-300 mb-3">
                  Password
                </label>
                <div className="relative group">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-cyber-primary/20 rounded-lg flex items-center justify-center group-focus-within:bg-cyber-primary/30 transition-colors duration-300">
                    <Lock className="h-3 w-3 text-cyber-primary" />
                  </div>
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full pl-14 pr-14 py-4 bg-gradient-glass border border-slate-700/50 rounded-2xl text-white placeholder-slate-400 focus:border-cyber-primary/50 focus:ring-2 focus:ring-cyber-primary/20 transition-all duration-300"
                    placeholder="SecureDemo123!"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-slate-700/50 rounded-lg flex items-center justify-center text-slate-400 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-all duration-300"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <label className="flex items-center group cursor-pointer">
                  <div className="relative">
                    <input
                      type="checkbox"
                      className="sr-only"
                    />
                    <div className="w-5 h-5 bg-gradient-glass border border-slate-700/50 rounded-lg group-hover:border-cyber-primary/30 transition-all duration-300 flex items-center justify-center">
                      <CheckCircle className="h-3 w-3 text-cyber-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </div>
                  <span className="ml-3 text-sm text-slate-300 group-hover:text-white transition-colors duration-300">
                    Remember this device
                  </span>
                </label>
                <Link
                  to="/forgot-password"
                  className="text-sm text-cyber-primary hover:text-cyber-primary-light transition-colors duration-300 font-medium"
                >
                  Forgot password?
                </Link>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-secondary text-white py-4 px-8 rounded-2xl font-semibold text-lg flex items-center justify-center space-x-3 hover:glow-primary transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isLoading ? (
                  <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <>
                    <Shield className="h-5 w-5" />
                    <span>Secure Sign In</span>
                    <ArrowRight className="h-5 w-5" />
                  </>
                )}
              </button>
            </form>

            <div className="mt-8 text-center">
              <div className="flex items-center justify-center space-x-4 mb-6">
                <div className="h-px bg-slate-700 flex-1"></div>
                <span className="text-slate-400 text-sm">New to Identity Guardian?</span>
                <div className="h-px bg-slate-700 flex-1"></div>
              </div>
              <Link
                to="/register"
                className="inline-flex items-center space-x-2 text-cyber-primary hover:text-cyber-primary-light transition-colors duration-300 font-semibold"
              >
                <span>Create Security Account</span>
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Right Side - Features */}
      <div className="hidden lg:flex flex-1 items-center justify-center p-8 relative">
        {/* Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-bl from-slate-900 via-purple-900/20 to-slate-900"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyber-secondary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

        <div className="relative max-w-xl z-10">
          <div className="text-center mb-16 fade-in">
            <div className="inline-flex items-center space-x-2 bg-gradient-glass border border-cyber-primary/30 rounded-full px-6 py-3 mb-8">
              <Shield className="h-4 w-4 text-cyber-primary" />
              <span className="text-sm font-medium text-cyber-primary">Enterprise Security Platform</span>
            </div>
            <h2 className="text-5xl font-bold text-white mb-6 text-gradient">
              Advanced Threat Protection
            </h2>
            <p className="text-xl text-slate-300 leading-relaxed">
              Military-grade cybersecurity monitoring and real-time threat intelligence
              to protect your digital assets and identity.
            </p>
          </div>

          <div className="space-y-6">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="cyber-card p-8 hover:glow-secondary transition-all duration-500 group"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="flex items-start space-x-6">
                    <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 group-hover:glow-secondary transition-all duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-cyber-primary transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-slate-300 leading-relaxed text-lg group-hover:text-slate-200 transition-colors duration-300">
                        {feature.description}
                      </p>
                      <div className="mt-4 h-1 bg-gradient-secondary rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Enhanced Trust Indicators */}
          <div className="mt-16 grid grid-cols-3 gap-6">
            <div className="cyber-card p-6 text-center hover:glow-success transition-all duration-500 group">
              <div className="w-12 h-12 bg-gradient-success rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-gradient">99.9%</div>
              <div className="text-sm text-slate-400 font-medium">System Uptime</div>
            </div>
            <div className="cyber-card p-6 text-center hover:glow-primary transition-all duration-500 group">
              <div className="w-12 h-12 bg-gradient-secondary rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-gradient">50M+</div>
              <div className="text-sm text-slate-400 font-medium">Identities Protected</div>
            </div>
            <div className="cyber-card p-6 text-center hover:glow-warning transition-all duration-500 group">
              <div className="w-12 h-12 bg-gradient-warning rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-gradient">24/7</div>
              <div className="text-sm text-slate-400 font-medium">Threat Monitoring</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;


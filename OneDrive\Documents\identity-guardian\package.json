{"name": "identity-guardian-complete", "version": "1.0.0", "description": "Complete Identity Guardian cybersecurity application package", "main": "identity-guardian-frontend/src/App.jsx", "scripts": {"install-backend": "cd identity-guardian-backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt", "install-frontend": "cd identity-guardian-frontend && npm install", "install-all": "npm run install-backend && npm run install-frontend", "start-backend": "cd identity-guardian-backend && source venv/bin/activate && python src/main.py", "start-frontend": "cd identity-guardian-frontend && npm run dev", "build-frontend": "cd identity-guardian-frontend && npm run build", "setup-db": "psql theguardian < theguardian_database_schema.sql", "dev": "concurrently \"npm run start-backend\" \"npm run start-frontend\"", "build": "npm run build-frontend && cp -r identity-guardian-frontend/dist/* identity-guardian-backend/src/static/"}, "keywords": ["cybersecurity", "identity-protection", "password-security", "breach-monitoring", "flask", "react", "postgresql", "white-hat", "security-education"], "author": "Identity Guardian Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/identity-guardian.git"}, "bugs": {"url": "https://github.com/your-username/identity-guardian/issues"}, "homepage": "https://github.com/your-username/identity-guardian#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0", "python": ">=3.9.0"}, "devDependencies": {"concurrently": "^8.2.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "lucide-react": "^0.263.1", "tailwindcss": "^3.3.3", "@headlessui/react": "^1.7.17", "@tailwindcss/forms": "^0.5.6"}, "frontendDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "vite": "^4.4.5"}, "backendDependencies": {"Flask": "2.3.3", "Flask-SQLAlchemy": "3.0.5", "Flask-Migrate": "4.0.5", "Flask-JWT-Extended": "4.5.3", "Flask-CORS": "4.0.0", "psycopg2-binary": "2.9.7", "python-dotenv": "1.0.0", "bcrypt": "4.0.1", "requests": "2.31.0"}}
import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS
from datetime import datetime
import hashlib
import secrets
import re

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
app.config['SECRET_KEY'] = 'asdf#FGSgvasgf$5$WGT'
CORS(app)  # Enable CORS for all routes

# Mock database for demo
users = {}
identities = {}
passwords = {}
alerts = []

# Demo user for testing
demo_user = {
    'id': 1,
    'email': '<EMAIL>',
    'password_hash': hashlib.sha256('SecureDemo123!'.encode()).hexdigest(),
    'first_name': 'Demo',
    'last_name': 'User',
    'created_at': datetime.now().isoformat()
}
users['<EMAIL>'] = demo_user

@app.route('/api')
def api_home():
    return {'message': 'Identity Guardian API', 'status': 'operational', 'version': '1.0.0'}

@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    email = data.get('email')
    password = data.get('password')
    
    if not email or not password:
        return jsonify({'error': 'Email and password required'}), 400
    
    user = users.get(email)
    if not user:
        return jsonify({'error': 'Invalid credentials'}), 401
    
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    if user['password_hash'] != password_hash:
        return jsonify({'error': 'Invalid credentials'}), 401
    
    # Generate mock token
    token = secrets.token_urlsafe(32)
    
    return jsonify({
        'token': token,
        'user': {
            'id': user['id'],
            'email': user['email'],
            'first_name': user['first_name'],
            'last_name': user['last_name'],
            'full_name': f"{user['first_name']} {user['last_name']}"
        }
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    data = request.get_json()
    email = data.get('email')
    password = data.get('password')
    first_name = data.get('first_name')
    last_name = data.get('last_name')
    
    if not all([email, password, first_name, last_name]):
        return jsonify({'error': 'All fields required'}), 400
    
    if email in users:
        return jsonify({'error': 'User already exists'}), 409
    
    # Create new user
    user_id = len(users) + 1
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    new_user = {
        'id': user_id,
        'email': email,
        'password_hash': password_hash,
        'first_name': first_name,
        'last_name': last_name,
        'created_at': datetime.now().isoformat()
    }
    
    users[email] = new_user
    
    # Generate mock token
    token = secrets.token_urlsafe(32)
    
    return jsonify({
        'token': token,
        'user': {
            'id': new_user['id'],
            'email': new_user['email'],
            'first_name': new_user['first_name'],
            'last_name': new_user['last_name'],
            'full_name': f"{new_user['first_name']} {new_user['last_name']}"
        }
    }), 201

@app.route('/api/auth/me', methods=['GET'])
def get_current_user():
    # Mock authentication - in real app, verify JWT token
    return jsonify({
        'id': demo_user['id'],
        'email': demo_user['email'],
        'first_name': demo_user['first_name'],
        'last_name': demo_user['last_name'],
        'full_name': f"{demo_user['first_name']} {demo_user['last_name']}"
    })

@app.route('/api/passwords/analyze', methods=['POST'])
def analyze_password():
    data = request.get_json()
    password = data.get('password', '')
    
    if not password:
        return jsonify({'error': 'Password required'}), 400
    
    # Password analysis logic
    length = len(password)
    has_upper = bool(re.search(r'[A-Z]', password))
    has_lower = bool(re.search(r'[a-z]', password))
    has_numbers = bool(re.search(r'\d', password))
    has_symbols = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
    
    score = 0
    strength = 'very_weak'
    issues = []
    suggestions = []
    
    # Length scoring
    if length >= 12:
        score += 25
    elif length >= 8:
        score += 15
    else:
        issues.append('Password is too short')
    
    # Character variety
    if has_upper:
        score += 15
    else:
        suggestions.append('Add uppercase letters')
    
    if has_lower:
        score += 15
    else:
        suggestions.append('Add lowercase letters')
    
    if has_numbers:
        score += 15
    else:
        suggestions.append('Add numbers')
    
    if has_symbols:
        score += 20
    else:
        suggestions.append('Add special characters')
    
    # Common patterns check
    if re.search(r'123|abc|qwe', password, re.IGNORECASE):
        score -= 10
        issues.append('Contains common patterns')
    
    # Determine strength
    if score >= 80:
        strength = 'very_strong'
    elif score >= 65:
        strength = 'strong'
    elif score >= 50:
        strength = 'good'
    elif score >= 35:
        strength = 'fair'
    elif score >= 20:
        strength = 'weak'
    
    return jsonify({
        'score': max(0, score),
        'strength': strength,
        'issues': issues,
        'suggestions': suggestions,
        'entropy': length * 4,
        'time_to_crack': 'Centuries' if score > 70 else 'Years' if score > 50 else 'Days' if score > 30 else 'Hours'
    })

@app.route('/api/passwords/generate', methods=['POST'])
def generate_password():
    data = request.get_json()
    length = data.get('length', 16)
    include_uppercase = data.get('include_uppercase', True)
    include_lowercase = data.get('include_lowercase', True)
    include_numbers = data.get('include_numbers', True)
    include_symbols = data.get('include_symbols', True)
    exclude_similar = data.get('exclude_similar', True)
    
    charset = ''
    if include_uppercase:
        charset += 'ABCDEFGHJKLMNPQRSTUVWXYZ' if exclude_similar else 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    if include_lowercase:
        charset += 'abcdefghjkmnpqrstuvwxyz' if exclude_similar else 'abcdefghijklmnopqrstuvwxyz'
    if include_numbers:
        charset += '23456789' if exclude_similar else '0123456789'
    if include_symbols:
        charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'
    
    if not charset:
        return jsonify({'error': 'At least one character type must be selected'}), 400
    
    password = ''.join(secrets.choice(charset) for _ in range(length))
    
    return jsonify({
        'password': password,
        'strength': 'very_strong',
        'score': 95,
        'entropy': length * 4
    })

@app.route('/api/passwords/check-breach', methods=['POST'])
def check_password_breach():
    data = request.get_json()
    password = data.get('password', '')
    
    # Mock breach check - in real app, use HaveIBeenPwned API
    common_passwords = ['password', '123456', 'password123', 'admin', 'qwerty']
    is_compromised = password.lower() in common_passwords
    
    return jsonify({
        'is_compromised': is_compromised,
        'breach_count': 5 if is_compromised else 0,
        'message': 'Password found in breach database' if is_compromised else 'Password not found in known breaches'
    })

@app.route('/api/identities', methods=['GET'])
def get_identities():
    # Mock identities data
    mock_identities = [
        {
            'id': 1,
            'type': 'email',
            'value': '<EMAIL>',
            'status': 'monitored',
            'risk_level': 'low',
            'breach_count': 0,
            'last_checked': '2 hours ago'
        },
        {
            'id': 2,
            'type': 'phone',
            'value': '+****************',
            'status': 'monitored',
            'risk_level': 'medium',
            'breach_count': 1,
            'last_checked': '1 hour ago'
        }
    ]
    return jsonify(mock_identities)

@app.route('/api/identities', methods=['POST'])
def add_identity():
    data = request.get_json()
    identity_type = data.get('type')
    value = data.get('value')
    
    if not identity_type or not value:
        return jsonify({'error': 'Type and value required'}), 400
    
    new_identity = {
        'id': len(identities) + 1,
        'type': identity_type,
        'value': value,
        'status': 'monitored',
        'risk_level': 'low',
        'breach_count': 0,
        'last_checked': 'Just now'
    }
    
    identities[new_identity['id']] = new_identity
    return jsonify(new_identity), 201

@app.route('/api/alerts', methods=['GET'])
def get_alerts():
    # Mock alerts data
    mock_alerts = [
        {
            'id': 1,
            'type': 'breach',
            'severity': 'high',
            'title': 'Email found in data breach',
            'description': 'Your email was found in a recent breach',
            'timestamp': datetime.now().isoformat(),
            'status': 'unread'
        }
    ]
    return jsonify(mock_alerts)

@app.route('/api/security-tips', methods=['GET'])
def get_security_tips():
    # Mock security tips
    mock_tips = [
        {
            'id': 1,
            'title': 'Use Strong Passwords',
            'category': 'passwords',
            'priority': 'critical',
            'content': 'Create unique, complex passwords for all accounts.',
            'read_time': 3
        },
        {
            'id': 2,
            'title': 'Enable Two-Factor Authentication',
            'category': 'authentication',
            'priority': 'high',
            'content': 'Add an extra layer of security to your accounts.',
            'read_time': 2
        }
    ]
    return jsonify(mock_tips)

@app.route('/api/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    return jsonify({
        'security_score': 85,
        'identities_monitored': 5,
        'breaches_detected': 2,
        'passwords_analyzed': 12,
        'weak_passwords': 3,
        'compromised_passwords': 1
    })

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)


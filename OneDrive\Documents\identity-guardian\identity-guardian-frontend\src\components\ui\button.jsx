import React from 'react';
import { cn } from '../../lib/utils';

const Button = React.forwardRef(({ className, variant = 'default', size = 'default', ...props }, ref) => {
  const variants = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
    // Enhanced cybersecurity variants
    cyber: 'bg-gradient-secondary text-white hover:glow-primary transform hover:scale-105 transition-all duration-300 relative overflow-hidden',
    'cyber-outline': 'border-2 border-cyber-primary text-cyber-primary bg-transparent hover:bg-cyber-primary hover:text-white transition-all duration-300',
    'cyber-ghost': 'text-cyber-primary hover:bg-cyber-primary/10 hover:text-cyber-primary-light transition-all duration-300',
    success: 'bg-gradient-success text-white hover:glow-success transform hover:scale-105 transition-all duration-300',
    warning: 'bg-gradient-warning text-white hover:glow-warning transform hover:scale-105 transition-all duration-300',
    danger: 'bg-gradient-danger text-white hover:glow-danger transform hover:scale-105 transition-all duration-300',
  };

  const sizes = {
    default: 'h-10 px-4 py-2',
    sm: 'h-9 rounded-md px-3',
    lg: 'h-11 rounded-md px-8',
    xl: 'h-14 rounded-2xl px-12 text-lg font-semibold',
    icon: 'h-10 w-10',
    'icon-sm': 'h-8 w-8',
    'icon-lg': 'h-12 w-12',
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        variants[variant],
        sizes[size],
        className
      )}
      ref={ref}
      {...props}
    />
  );
});

Button.displayName = 'Button';

export { Button };


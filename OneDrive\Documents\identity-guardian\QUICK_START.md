# 🚀 Identity Guardian - Quick Start Guide

## ⚡ 5-Minute Setup

### 1. Prerequisites Check
```bash
# Check if you have the required software
node --version    # Should be v18+
python --version  # Should be v3.9+
psql --version    # Should be v13+
```

### 2. Database Setup (2 minutes)
```bash
# Create database
createdb theguardian

# Import schema
psql theguardian < theguardian_database_schema.sql
```

### 3. Backend Setup (2 minutes)
```bash
cd identity-guardian-backend

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # macOS/Linux
# OR
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
```

**Edit `.env` file:**
```bash
DATABASE_URL=postgresql://your_username:your_password@localhost:5432/theguardian
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key
```

### 4. Frontend Setup (1 minute)
```bash
cd identity-guardian-frontend
npm install
```

### 5. Run Application
```bash
# Terminal 1 - Backend
cd identity-guardian-backend
source venv/bin/activate
python src/main.py

# Terminal 2 - Frontend (optional, for development)
cd identity-guardian-frontend
npm run dev
```

## 🎯 Access Your Application

- **Application**: http://localhost:5000
- **Frontend Dev Server**: http://localhost:5173 (if running separately)

## 🔐 Demo Credentials

- **Email**: <EMAIL>
- **Password**: SecureDemo123!

## 🛠️ VSCode Setup

1. **Open folder** in VSCode
2. **Install recommended extensions** (VSCode will prompt you)
3. **Use Command Palette** (Ctrl/Cmd + Shift + P):
   - `Tasks: Run Task` → `🐍 Start Backend Server`
   - `Tasks: Run Task` → `⚛️ Start Frontend Server`

## 🔧 Common Issues

### Database Connection Error
```bash
# Check PostgreSQL is running
brew services start postgresql  # macOS
sudo systemctl start postgresql # Linux
net start postgresql-x64-13     # Windows
```

### Python Virtual Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Node.js Issues
```bash
# Clear cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## 🎨 What You'll See

✨ **Beautiful animated gradient backgrounds**
✨ **Glass morphism UI with professional blur effects**
✨ **Glowing buttons and smooth hover animations**
✨ **Modern cybersecurity aesthetic**
✨ **Responsive design for all devices**

## 📚 Next Steps

1. **Explore the demo** with provided credentials
2. **Read `installation_guide.md`** for detailed setup
3. **Check `identity_guardian_demo_guide.md`** for features
4. **Customize the application** for your needs

---

**🛡️ Identity Guardian is ready to protect digital identities!**


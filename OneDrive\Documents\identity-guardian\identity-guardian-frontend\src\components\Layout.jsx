import {
    Activity,
    BookOpen,
    Eye,
    Key,
    LayoutDashboard,
    LogOut,
    Menu,
    Settings,
    Shield,
    User,
    X
} from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '../contexts/AuthContext';
import { Badge } from './ui/badge';
import { Button } from './ui/button';

const Layout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      current: location.pathname === '/dashboard'
    },
    {
      name: 'Identities',
      href: '/identities',
      icon: Eye,
      current: location.pathname === '/identities'
    },
    {
      name: 'Passwords',
      href: '/passwords',
      icon: Key,
      current: location.pathname === '/passwords'
    },
    {
      name: 'Monitoring',
      href: '/monitoring',
      icon: Activity,
      current: location.pathname === '/monitoring'
    },
    {
      name: 'Security Tips',
      href: '/security-tips',
      icon: BookOpen,
      current: location.pathname === '/security-tips'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      current: location.pathname === '/settings'
    }
  ];

  const handleLogout = () => {
    logout();
    toast.success('Logged out successfully');
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Enhanced Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-72 bg-gradient-dark border-r border-cyber-primary/20 transform transition-all duration-300 ease-in-out lg:translate-x-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex flex-col h-full cyber-card border-0 rounded-none">
          {/* Enhanced Logo */}
          <div className="flex items-center justify-between p-8 border-b border-cyber-primary/20">
            <Link to="/dashboard" className="flex items-center space-x-4 group">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-secondary rounded-2xl flex items-center justify-center glow-primary group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-cyber-accent-green rounded-full animate-pulse"></div>
              </div>
              <div>
                <span className="text-2xl font-bold text-white group-hover:text-cyber-primary transition-colors duration-300">
                  Identity Guardian
                </span>
                <div className="text-xs text-cyber-primary font-medium">Enterprise Security</div>
              </div>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-slate-400 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-all duration-300"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Enhanced User info */}
          <div className="p-8 border-b border-cyber-primary/20">
            <div className="flex items-center space-x-4 mb-4">
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-secondary rounded-2xl flex items-center justify-center glow-secondary">
                  <User className="h-7 w-7 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-cyber-accent-green rounded-full border-2 border-slate-900 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              <div className="flex-1">
                <p className="text-white font-bold text-lg">{user?.full_name || 'Demo User'}</p>
                <p className="text-slate-400 text-sm">{user?.email || '<EMAIL>'}</p>
              </div>
            </div>

            {/* Status Badges */}
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-cyber-accent-green/20 text-cyber-accent-green border-cyber-accent-green/30 hover:bg-cyber-accent-green/30 transition-colors duration-300">
                <Shield className="w-3 h-3 mr-1" />
                Protected
              </Badge>
              <Badge className="bg-cyber-primary/20 text-cyber-primary border-cyber-primary/30 hover:bg-cyber-primary/30 transition-colors duration-300">
                Premium
              </Badge>
            </div>
          </div>

          {/* Enhanced Navigation */}
          <nav className="flex-1 p-8 space-y-3">
            <div className="text-xs font-semibold text-slate-500 uppercase tracking-wider mb-4">
              Security Center
            </div>
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`
                    group flex items-center space-x-4 px-4 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 relative overflow-hidden
                    ${item.current
                      ? 'bg-gradient-secondary text-white glow-primary'
                      : 'text-slate-300 hover:text-white hover:bg-gradient-glass border border-transparent hover:border-cyber-primary/30'
                    }
                  `}
                  onClick={() => setSidebarOpen(false)}
                >
                  {/* Background effect for active item */}
                  {item.current && (
                    <div className="absolute inset-0 bg-gradient-to-r from-cyber-primary/20 to-transparent opacity-50"></div>
                  )}

                  <div className={`relative z-10 w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
                    item.current
                      ? 'bg-white/20'
                      : 'bg-slate-800/50 group-hover:bg-cyber-primary/20'
                  }`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <span className="relative z-10 flex-1">{item.name}</span>

                  {/* Active indicator */}
                  {item.current && (
                    <div className="relative z-10 w-2 h-2 bg-cyber-accent-green rounded-full animate-pulse"></div>
                  )}
                </Link>
              );
            })}
          </nav>

          {/* Enhanced Logout */}
          <div className="p-8 border-t border-cyber-primary/20">
            <Button
              variant="ghost"
              className="w-full justify-start text-slate-300 hover:text-cyber-accent-red hover:bg-cyber-accent-red/10 border border-transparent hover:border-cyber-accent-red/30 rounded-2xl py-3 px-4 transition-all duration-300 group"
              onClick={handleLogout}
            >
              <div className="w-10 h-10 rounded-xl bg-slate-800/50 group-hover:bg-cyber-accent-red/20 flex items-center justify-center mr-4 transition-all duration-300">
                <LogOut className="h-5 w-5" />
              </div>
              <span className="font-semibold">Sign Out</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Main content */}
      <div className="lg:pl-72">
        {/* Enhanced Top bar */}
        <div className="sticky top-0 z-30 bg-gradient-dark/95 backdrop-blur-sm border-b border-cyber-primary/20 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden text-slate-400 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-all duration-300"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-6 w-6" />
              </Button>

              {/* Breadcrumb or page title could go here */}
              <div className="hidden md:flex items-center space-x-2 text-sm text-slate-400">
                <span>Security Dashboard</span>
                <span>/</span>
                <span className="text-cyber-primary font-medium">Overview</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Security Status Badge */}
              <Badge className="bg-cyber-accent-green/20 text-cyber-accent-green border-cyber-accent-green/30 hover:bg-cyber-accent-green/30 transition-colors duration-300">
                <Shield className="w-3 h-3 mr-1" />
                White Hat Security
              </Badge>

              {/* Live Status Indicator */}
              <div className="hidden sm:flex items-center space-x-2 bg-gradient-glass border border-cyber-primary/30 rounded-full px-4 py-2">
                <div className="w-2 h-2 bg-cyber-accent-green rounded-full animate-pulse"></div>
                <span className="text-xs font-medium text-cyber-primary">Live Monitoring</span>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Page content */}
        <main className="min-h-screen bg-gradient-dark">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;


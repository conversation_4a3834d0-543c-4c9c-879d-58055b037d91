import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Textarea } from '../components/ui/textarea';
import { Switch } from '../components/ui/switch';
import { 
  Key, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Copy, 
  RefreshCw, 
  Eye, 
  EyeOff,
  Zap,
  Lock,
  Unlock,
  TrendingUp,
  Download,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';

const PasswordsPage = () => {
  const [passwords, setPasswords] = useState([
    {
      id: 1,
      service: 'Gmail',
      username: '<EMAIL>',
      strength: 'strong',
      score: 85,
      isCompromised: false,
      lastChecked: '2 hours ago',
      recommendations: ['Consider using a longer password', 'Add special characters']
    },
    {
      id: 2,
      service: 'Facebook',
      username: '<EMAIL>',
      strength: 'weak',
      score: 35,
      isCompromised: false,
      lastChecked: '1 day ago',
      recommendations: ['Use a mix of uppercase and lowercase', 'Add numbers and symbols', 'Increase length to at least 12 characters']
    },
    {
      id: 3,
      service: 'LinkedIn',
      username: '<EMAIL>',
      strength: 'compromised',
      score: 0,
      isCompromised: true,
      lastChecked: '3 hours ago',
      recommendations: ['Change password immediately', 'Enable two-factor authentication', 'Use a unique password']
    }
  ]);

  const [newPassword, setNewPassword] = useState('');
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [passwordAnalysis, setPasswordAnalysis] = useState(null);
  const [generatorSettings, setGeneratorSettings] = useState({
    length: 16,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSymbols: true,
    excludeSimilar: true
  });

  const analyzePassword = (password) => {
    // Mock password analysis
    const length = password.length;
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSymbols = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    let score = 0;
    let strength = 'very_weak';
    let issues = [];
    let suggestions = [];

    // Length scoring
    if (length >= 12) score += 25;
    else if (length >= 8) score += 15;
    else issues.push('Password is too short');

    // Character variety
    if (hasUpper) score += 15;
    else suggestions.push('Add uppercase letters');
    
    if (hasLower) score += 15;
    else suggestions.push('Add lowercase letters');
    
    if (hasNumbers) score += 15;
    else suggestions.push('Add numbers');
    
    if (hasSymbols) score += 20;
    else suggestions.push('Add special characters');

    // Common patterns check
    if (/123|abc|qwe/i.test(password)) {
      score -= 10;
      issues.push('Contains common patterns');
    }

    // Determine strength
    if (score >= 80) strength = 'very_strong';
    else if (score >= 65) strength = 'strong';
    else if (score >= 50) strength = 'good';
    else if (score >= 35) strength = 'fair';
    else if (score >= 20) strength = 'weak';

    return {
      score: Math.max(0, score),
      strength,
      issues,
      suggestions,
      entropy: length * 4, // Simplified entropy calculation
      timeToCrack: score > 70 ? 'Centuries' : score > 50 ? 'Years' : score > 30 ? 'Days' : 'Hours'
    };
  };

  const generatePassword = () => {
    const { length, includeUppercase, includeLowercase, includeNumbers, includeSymbols, excludeSimilar } = generatorSettings;
    
    let charset = '';
    if (includeUppercase) charset += excludeSimilar ? 'ABCDEFGHJKLMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (includeLowercase) charset += excludeSimilar ? 'abcdefghjkmnpqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz';
    if (includeNumbers) charset += excludeSimilar ? '23456789' : '0123456789';
    if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    setGeneratedPassword(password);
    return password;
  };

  const generatePassphrase = () => {
    const words = ['apple', 'bridge', 'castle', 'dragon', 'eagle', 'forest', 'guitar', 'harbor', 'island', 'jungle'];
    const selectedWords = [];
    for (let i = 0; i < 4; i++) {
      selectedWords.push(words[Math.floor(Math.random() * words.length)]);
    }
    const passphrase = selectedWords.join('-') + Math.floor(Math.random() * 100);
    setGeneratedPassword(passphrase);
    return passphrase;
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const getStrengthColor = (strength) => {
    switch (strength) {
      case 'very_strong': return 'text-green-400';
      case 'strong': return 'text-green-400';
      case 'good': return 'text-yellow-400';
      case 'fair': return 'text-yellow-400';
      case 'weak': return 'text-red-400';
      case 'very_weak': return 'text-red-400';
      case 'compromised': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  const getStrengthBadge = (strength, isCompromised) => {
    if (isCompromised) {
      return <Badge variant="destructive" className="bg-red-500/20 text-red-300">Compromised</Badge>;
    }
    
    switch (strength) {
      case 'very_strong':
        return <Badge className="bg-green-500/20 text-green-300 border-green-500/50">Very Strong</Badge>;
      case 'strong':
        return <Badge className="bg-green-500/20 text-green-300 border-green-500/50">Strong</Badge>;
      case 'good':
        return <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/50">Good</Badge>;
      case 'fair':
        return <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/50">Fair</Badge>;
      case 'weak':
        return <Badge variant="destructive" className="bg-red-500/20 text-red-300">Weak</Badge>;
      default:
        return <Badge variant="destructive" className="bg-red-500/20 text-red-300">Very Weak</Badge>;
    }
  };

  useEffect(() => {
    if (newPassword) {
      const analysis = analyzePassword(newPassword);
      setPasswordAnalysis(analysis);
    } else {
      setPasswordAnalysis(null);
    }
  }, [newPassword]);

  const dashboardStats = {
    totalPasswords: passwords.length,
    strongPasswords: passwords.filter(p => ['strong', 'very_strong'].includes(p.strength)).length,
    weakPasswords: passwords.filter(p => ['weak', 'very_weak', 'fair'].includes(p.strength)).length,
    compromisedPasswords: passwords.filter(p => p.isCompromised).length,
    averageScore: Math.round(passwords.reduce((sum, p) => sum + p.score, 0) / passwords.length)
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Password Security</h1>
            <p className="text-slate-400">
              Analyze, generate, and manage secure passwords to protect your accounts
            </p>
          </div>
          <Button className="bg-purple-600 hover:bg-purple-700 mt-4 md:mt-0">
            <Plus className="w-4 h-4 mr-2" />
            Add Password
          </Button>
        </div>

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Passwords</CardTitle>
              <Key className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{dashboardStats.totalPasswords}</div>
              <p className="text-xs text-slate-400">Monitored accounts</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Strong Passwords</CardTitle>
              <Shield className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{dashboardStats.strongPasswords}</div>
              <p className="text-xs text-slate-400">Well protected</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Weak Passwords</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{dashboardStats.weakPasswords}</div>
              <p className="text-xs text-slate-400">Need improvement</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Compromised</CardTitle>
              <Unlock className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{dashboardStats.compromisedPasswords}</div>
              <p className="text-xs text-slate-400">Immediate action needed</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="analyzer" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-slate-800/50">
            <TabsTrigger value="analyzer" className="data-[state=active]:bg-purple-600">
              Password Analyzer
            </TabsTrigger>
            <TabsTrigger value="generator" className="data-[state=active]:bg-purple-600">
              Password Generator
            </TabsTrigger>
            <TabsTrigger value="vault" className="data-[state=active]:bg-purple-600">
              Password Vault
            </TabsTrigger>
            <TabsTrigger value="tips" className="data-[state=active]:bg-purple-600">
              Security Tips
            </TabsTrigger>
          </TabsList>

          {/* Password Analyzer */}
          <TabsContent value="analyzer" className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Shield className="w-5 h-5 mr-2 text-purple-400" />
                  Password Strength Analyzer
                </CardTitle>
                <CardDescription>
                  Check if your password is strong and secure against common attacks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password-input" className="text-slate-300">Enter Password to Analyze</Label>
                  <div className="relative">
                    <Input
                      id="password-input"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password here..."
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                {passwordAnalysis && (
                  <div className="space-y-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                    <div className="flex items-center justify-between">
                      <span className="text-slate-300">Strength Score</span>
                      <span className={`text-2xl font-bold ${getStrengthColor(passwordAnalysis.strength)}`}>
                        {passwordAnalysis.score}/100
                      </span>
                    </div>
                    
                    <Progress value={passwordAnalysis.score} className="h-3" />
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-white font-medium mb-2">Analysis Results</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-400">Strength Level:</span>
                            <span className={getStrengthColor(passwordAnalysis.strength)}>
                              {passwordAnalysis.strength.replace('_', ' ').toUpperCase()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Entropy:</span>
                            <span className="text-white">{passwordAnalysis.entropy} bits</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Time to Crack:</span>
                            <span className="text-white">{passwordAnalysis.timeToCrack}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-white font-medium mb-2">Recommendations</h4>
                        <div className="space-y-1">
                          {passwordAnalysis.suggestions.map((suggestion, index) => (
                            <div key={index} className="flex items-start space-x-2">
                              <CheckCircle className="h-3 w-3 text-green-400 mt-0.5 flex-shrink-0" />
                              <span className="text-xs text-slate-300">{suggestion}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {passwordAnalysis.issues.length > 0 && (
                      <Alert className="border-red-500/50 bg-red-500/10">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-red-300">
                          <strong>Issues found:</strong> {passwordAnalysis.issues.join(', ')}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Password Generator */}
          <TabsContent value="generator" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Zap className="w-5 h-5 mr-2 text-yellow-400" />
                    Password Generator
                  </CardTitle>
                  <CardDescription>
                    Generate cryptographically secure passwords
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-slate-300">Length: {generatorSettings.length}</Label>
                      <input
                        type="range"
                        min="8"
                        max="64"
                        value={generatorSettings.length}
                        onChange={(e) => setGeneratorSettings({...generatorSettings, length: parseInt(e.target.value)})}
                        className="w-full"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={generatorSettings.includeUppercase}
                          onCheckedChange={(checked) => setGeneratorSettings({...generatorSettings, includeUppercase: checked})}
                        />
                        <Label className="text-slate-300 text-sm">Uppercase</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={generatorSettings.includeLowercase}
                          onCheckedChange={(checked) => setGeneratorSettings({...generatorSettings, includeLowercase: checked})}
                        />
                        <Label className="text-slate-300 text-sm">Lowercase</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={generatorSettings.includeNumbers}
                          onCheckedChange={(checked) => setGeneratorSettings({...generatorSettings, includeNumbers: checked})}
                        />
                        <Label className="text-slate-300 text-sm">Numbers</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={generatorSettings.includeSymbols}
                          onCheckedChange={(checked) => setGeneratorSettings({...generatorSettings, includeSymbols: checked})}
                        />
                        <Label className="text-slate-300 text-sm">Symbols</Label>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button onClick={generatePassword} className="flex-1 bg-purple-600 hover:bg-purple-700">
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Generate Password
                      </Button>
                      <Button onClick={generatePassphrase} variant="outline" className="border-slate-600 text-slate-300">
                        Passphrase
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Generated Password</CardTitle>
                  <CardDescription>
                    Your secure password is ready to use
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {generatedPassword && (
                    <>
                      <div className="relative">
                        <Textarea
                          value={generatedPassword}
                          readOnly
                          className="bg-slate-700/50 border-slate-600 text-white font-mono text-lg resize-none"
                          rows={3}
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          className="absolute top-2 right-2 text-slate-400 hover:text-white"
                          onClick={() => copyToClipboard(generatedPassword)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-slate-400">Length:</span>
                            <span className="text-white">{generatedPassword.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Strength:</span>
                            <span className="text-green-400">Very Strong</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-slate-400">Entropy:</span>
                            <span className="text-white">{generatedPassword.length * 4} bits</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Time to Crack:</span>
                            <span className="text-white">Centuries</span>
                          </div>
                        </div>
                      </div>

                      <Button 
                        className="w-full bg-green-600 hover:bg-green-700"
                        onClick={() => copyToClipboard(generatedPassword)}
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Copy to Clipboard
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Password Vault */}
          <TabsContent value="vault" className="space-y-6">
            <div className="space-y-4">
              {passwords.map((password) => (
                <Card key={password.id} className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
                          <Key className="h-6 w-6 text-purple-400" />
                        </div>
                        <div>
                          <h3 className="text-white font-medium">{password.service}</h3>
                          <p className="text-slate-400 text-sm">{password.username}</p>
                          <p className="text-slate-500 text-xs">Last checked: {password.lastChecked}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className={`text-lg font-bold ${getStrengthColor(password.strength)}`}>
                              {password.score}
                            </span>
                            {getStrengthBadge(password.strength, password.isCompromised)}
                          </div>
                          <Progress value={password.score} className="w-24 h-2" />
                        </div>
                        
                        <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {password.recommendations.length > 0 && (
                      <div className="mt-4 p-3 bg-slate-700/30 rounded-lg">
                        <h4 className="text-white text-sm font-medium mb-2">Recommendations:</h4>
                        <ul className="space-y-1">
                          {password.recommendations.map((rec, index) => (
                            <li key={index} className="text-slate-300 text-xs flex items-start space-x-2">
                              <CheckCircle className="h-3 w-3 text-yellow-400 mt-0.5 flex-shrink-0" />
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Security Tips */}
          <TabsContent value="tips" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Password Best Practices</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Use unique passwords for every account</p>
                      <p className="text-slate-400 text-sm">Never reuse passwords across multiple services</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Make passwords at least 12 characters long</p>
                      <p className="text-slate-400 text-sm">Longer passwords are exponentially harder to crack</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Use a mix of character types</p>
                      <p className="text-slate-400 text-sm">Include uppercase, lowercase, numbers, and symbols</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Avoid personal information</p>
                      <p className="text-slate-400 text-sm">Don't use names, birthdays, or other personal data</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Advanced Security</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Enable two-factor authentication</p>
                      <p className="text-slate-400 text-sm">Add an extra layer of security to your accounts</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Use a password manager</p>
                      <p className="text-slate-400 text-sm">Generate and store unique passwords securely</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Regular security audits</p>
                      <p className="text-slate-400 text-sm">Check for compromised passwords regularly</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-white font-medium">Monitor for breaches</p>
                      <p className="text-slate-400 text-sm">Stay informed about data breaches affecting your accounts</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default PasswordsPage;


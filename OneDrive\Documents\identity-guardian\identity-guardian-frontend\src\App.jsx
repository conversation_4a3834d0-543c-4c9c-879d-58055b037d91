import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './components/theme-provider';
import { Toaster } from './components/ui/sonner';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';

// Pages
import LandingPage from './pages/LandingPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import IdentitiesPage from './pages/IdentitiesPage';
import PasswordsPage from './pages/PasswordsPage';
import MonitoringPage from './pages/MonitoringPage';
import SecurityTipsPage from './pages/SecurityTipsPage';
import SettingsPage from './pages/SettingsPage';

function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="identity-guardian-theme">
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-background">
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<LandingPage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              
              {/* Protected routes */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <DashboardPage />
                </ProtectedRoute>
              } />
              <Route path="/identities" element={
                <ProtectedRoute>
                  <IdentitiesPage />
                </ProtectedRoute>
              } />
              <Route path="/passwords" element={
                <ProtectedRoute>
                  <PasswordsPage />
                </ProtectedRoute>
              } />
              <Route path="/monitoring" element={
                <ProtectedRoute>
                  <MonitoringPage />
                </ProtectedRoute>
              } />
              <Route path="/security-tips" element={
                <ProtectedRoute>
                  <SecurityTipsPage />
                </ProtectedRoute>
              } />
              <Route path="/settings" element={
                <ProtectedRoute>
                  <SettingsPage />
                </ProtectedRoute>
              } />
              
              {/* Redirect unknown routes */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            <Toaster />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;


# Identity Guardian Environment Configuration
# Generated on 2025-01-10

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/theguardian
DATABASE_URL_TEST=postgresql://username:password@localhost:5432/theguardian_test

# Flask Configuration
SECRET_KEY=jsrHz,.>LM&9>fsd!Oemm_Q5?fW$;%e1ZK$tTp:<^*f^qsW@NsRH;_x#t4=K@P)0
JWT_SECRET_KEY=<wr$0|INTDFCg=qY,!!^M6lu!w1)14{_3^2<Z0hkiwDF}yw03P.T{g7vf{2+O][#
FLASK_ENV=development

# External API Keys (Optional - Add your own keys when needed)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

HIBP_API_KEY=your-haveibeenpwned-api-key
NUMVERIFY_API_KEY=your-numverify-api-key
HUNTER_API_KEY=your-hunter-io-api-key

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Security Configuration
BCRYPT_LOG_ROUNDS=12

# Application Configuration
APP_NAME=Identity Guardian
APP_VERSION=1.0.0

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# JWT Configuration
JWT_ACCESS_TOKEN_EXPIRES=1
JWT_REFRESH_TOKEN_EXPIRES=30

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/1
